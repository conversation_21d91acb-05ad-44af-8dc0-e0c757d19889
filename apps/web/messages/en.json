{"agent": {"session": {"title": "Session", "notFound": "Session not found", "notFoundMessage": "Specified session not found", "loadingSessionInfo": "Loading session information..."}, "tips": {"success": "Success", "updateSuccess": "Agent updated successfully", "deleteSuccess": "Agent deleted successfully", "updateFailed": "Failed to update agent", "deleteFailed": "Failed to delete agent"}, "actions": {"edit": "Edit", "delete": "Delete", "editAgent": "Edit Agent", "deleteAgent": "Delete Agent", "batchDeleteSessions": "<PERSON><PERSON> Delete Sessions", "clearSessions": "Clear Sessions"}, "common": {"error": "Error", "loading": "Loading...", "loadingMessage": "Loading agent information...", "notFound": "Agent not found", "notFoundMessage": "Specified agent not found", "untitled": "Untitled Agent", "agentList": "Agent List", "chat": "Cha<PERSON>", "assistant": "Assistant", "modelNotConfigured": "Agent model not configured, unable to chat. Please configure a model."}, "meta": {"createTime": "Created:", "creator": "Creator:", "updateTime": "Updated:", "updator": "Modified by:"}, "delete": {"confirmTitle": "Confirm Delete", "confirmDescription": "Are you sure you want to delete this agent? This action cannot be undone, and all related sessions and data will be permanently deleted.", "personalDescription": "Are you sure you want to delete this Agent? This action cannot be undone, and all related sessions and data will be permanently deleted.", "teamDescription": "Are you sure you want to delete this Agent? After deleting a team agent, related sessions and data of other users using this agent will also be permanently deleted.", "confirmText": "Delete", "cancelText": "Cancel", "success": "Agent deleted successfully", "failed": "Failed to delete agent"}, "batchDelete": {"title": "Confirm Batch Delete", "description": "Are you sure you want to delete the selected {count} sessions? This action cannot be undone, and all selected sessions will be permanently deleted.", "confirmText": "Confirm Delete", "cancelText": "Cancel", "success": "Successfully deleted {count} sessions", "failed": "Failed to batch delete sessions", "noSelection": "Please select sessions to delete", "error": "Error"}, "clear": {"title": "Confirm Clear", "description": "After clearing {itemType}, all related {itemType} will be permanently deleted.", "confirmText": "Confirm Clear", "cancelText": "Cancel", "success": "Sessions cleared successfully", "failed": "Failed to clear sessions"}, "dialog": {"title": "Chat Configuration", "description": "Configure your dedicated assistant for your professional knowledge base here!", "cancel": "Cancel", "confirm": "Confirm"}, "form": {"resourceType": "Resource Type", "knowledgeBase": "Knowledge Base", "datasets": "Datasets", "database": "Database", "file": "File", "name": "Agent Name", "namePlaceholder": "Test Agent", "description": "Agent Description", "descriptionPlaceholder": "Enter a brief introduction about the Agent", "emptyReply": "Empty Reply", "emptyReplyPlaceholder": "e.g. Sorry, I cannot answer this question.", "greeting": "Set Greeting", "greetingPlaceholder": "Hello! I am your assistant, how can I help you?", "historyContext": "History Message Context (Count)", "supportedLanguages": "Supported Languages", "supportedLanguagesPlaceholder": "Please select supported languages", "supportedLanguagesDescription": "Select the languages supported by the Agent, at least one language must be selected", "agentPermission": "Agent Permission", "personal": "Personal", "team": "Team", "systemPrompt": "System Prompt", "systemPromptPlaceholder": "e.g. You are an expert in the academic field, please answer questions in as much detail as possible based on the content of the knowledge base.", "summaryPrompt": "Summary Prompt", "summaryPromptPlaceholder": "e.g. Please summarize the dataset based on the dataset content.", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "keywordWeight": "Keyword Similarity Weight", "topN": "Top N", "model": "Model", "modelPlaceholder": "Please select a model", "temperature": "Temperature", "topP": "Top P", "validation": {"resourceTypeRequired": "Please select resource type", "resourceRequired": "Please select resource", "nameRequired": "Agent name cannot be empty", "languageRequired": "Please select at least one language", "permissionRequired": "Please select Agent permission", "modelRequired": "Please select a model"}, "modelNotConfiguredMessage": "Team has not configured {modelType} model, please contact administrator for configuration", "chatModel": "Cha<PERSON>", "datasetChatModel": "Dataset Chat"}, "tabs": {"basicSettings": "Basic Settings", "promptEngine": "Prompt Engine", "modelSettings": "Model Settings"}, "details": {"basicInfo": "Basic Information", "agentName": "Agent Name", "resourceType": "Resource Type", "agentPermission": "Agent Permission", "supportedLanguages": "Supported Languages", "agentDescription": "Agent Description", "greeting": "Greeting", "emptyReply": "Empty Reply", "historyContext": "History Message Context (Count)", "promptSettings": "Prompt Settings", "systemPrompt": "System Prompt", "summaryPrompt": "Summary Prompt", "retrievalSettings": "Retrieval Settings", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "keywordWeight": "Keyword Weight", "retrievalCount": "Retrieval Count", "modelConfig": "Model Configuration", "model": "Model", "temperature": "Temperature", "topP": "Top P", "noDescription": "No Description", "notSet": "Not Set", "notConfigured": "Not Configured", "none": "None", "notSetYet": "Not Set Yet", "resourceTypes": {"knowledgeBase": "Knowledge Base", "databaseDataset": "Database Dataset", "fileDataset": "File Dataset"}}}, "session": {"title": "Session", "notFound": "Session not found", "notFoundMessage": "Specified session not found", "loadingSessionInfo": "Loading session information..."}, "team": {"title": "Team Members", "addMember": "Add Member", "accountName": "Account Name", "role": "Role", "actions": "Actions", "admin": "Admin", "member": "Member", "delete": "Delete", "deleteMemberTip": "Delete Member", "addMembers": {"title": "Add Team Members", "description": "Select members to add to the team from existing accounts", "addButton": "Add Members", "searchPlaceholder": "Search members...", "emptyText": "No matching members found", "loadingText": "Loading...", "endText": "Reached end of list", "selectedCount": "Selected {count} members", "unknownUser": "Unknown User"}, "deleteMember": {"title": "Confirm Delete", "description": "Are you sure you want to delete member \"{memberName}\"? This action cannot be undone."}, "messages": {"addSuccess": "Member added successfully", "addFailed": "Failed to add member", "deleteSuccess": "Member deleted successfully", "deleteFailed": "Failed to delete member", "fetchFailed": "Failed to fetch available members"}}, "model": {"title": "Set Default Models", "description": "You must select the required models before use", "chatModel": "Chat Model", "embeddingModel": "Embedding Model", "rerankModel": "<PERSON><PERSON>", "nl2sqlModel": "NL2SQL Model", "nl2pythonModel": "NL2PYTHON Model", "placeholder": "Please select an appropriate model", "cancel": "Cancel", "confirm": "Confirm", "submitting": "Submitting...", "validation": {"selectModel": "Please select an appropriate model", "selectChatModel": "Please select an appropriate chat model", "selectEmbeddingModel": "Please select an appropriate embedding model"}}, "knowledgeBase": {"title": "Knowledge Base Management", "addNew": "Add New Knowledge Base", "loading": "Loading...", "loadError": "Failed to load knowledge base list: {error}", "retry": "Retry", "unknownError": "Unknown error", "create": "Create Knowledge Base", "saveChanges": "Save Changes", "card": {"description": "Description:", "fileCount": "File Count:", "filesCount": "{count} files", "creator": "Creator:", "edit": "Edit", "delete": "Delete", "noPermissionDelete": "You don't have permission to delete"}, "delete": {"title": "Confirm Delete", "description": "If the knowledge base is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone."}, "form": {"name": "Knowledge Base Name", "nameRequired": "Knowledge base name cannot be empty", "description": "Knowledge Base Description", "defaultSliceMethod": "Default Document Slicing Method", "sliceMethodRequired": "Slicing method cannot be empty", "sliceMethodDescription": "Select the method for document slicing", "pdfParser": "PDF Parser", "usesDeepDoc": "Use DeepDOC to parse PDF documents"}, "fileSelection": {"title": "Select files for knowledge base \"{name}\"", "description": "Please select files to add to the knowledge base", "selectedCount": "Selected {count} files", "confirm": "Confirm Add", "searchPlaceholder": "Search files...", "emptyText": "No files found", "loadingText": "Loading...", "endText": "All files loaded", "rootDirectory": "Root Directory", "breadcrumbHome": "Home"}, "details": {"title": "Knowledge Base Details", "backToList": "Knowledge Base List", "notFound": "Specified knowledge base not found", "loading": "Loading...", "loadingError": "Failed to load knowledge base details", "addFiles": "Add Files", "fileName": "Name", "fileSize": "File Size", "chunkCount": "Chunks", "sliceMethod": "Slice Method", "chunkSize": "Chunk Size", "status": "Status", "progress": "Progress", "updateTime": "Update Time", "actions": "Actions", "selectAll": "Select All", "batchDelete": "<PERSON><PERSON> Delete", "batchParse": "<PERSON><PERSON> Parse", "batchStop": "Batch Stop", "batchStart": "<PERSON><PERSON> Start", "noSelection": "Please select files to operate on first", "selectedFilesCount": "Selected {count} items", "clearSelection": "Clear Selection", "createTime": "Create Time", "defaultSliceMethod": "De<PERSON><PERSON>", "documentCount": "Document Count", "creator": "Creator", "notSet": "Not Set", "successTitle": "Success", "errorTitle": "Error", "noFilesToParse": "No files to parse", "filesInParseQueue": "Added {count} files to parse queue", "batchOperationFailed": "Batch operation failed, please retry", "noFilesToStop": "No files currently parsing", "batchStopSuccess": "{count} files are being cancelled", "addFilesSuccess": "Successfully added {count} files to knowledge base", "addFilesFailed": "Failed to add files, please retry", "stopParseSuccess": "File parsing stopped", "startParseSuccess": "File parsing started", "operationFailed": "Operation failed, please retry", "documentDisabled": "Document disabled", "documentEnabled": "Document enabled", "chunkMethodSetSuccess": "Chunk method set successfully", "chunkMethodSetFailed": "Failed to set chunk method, please retry", "noPermissionBatchDelete": "You don't have permission to delete these files", "defaultKnowledgeBaseName": "Knowledge Base", "cannotSetChunkMethod": "Cannot set chunk method in current state", "setChunkMethod": "Set <PERSON> Method", "enabled": "Enabled", "createdBy": "Created By", "createTime2": "Create Time", "updatedBy": "Updated By", "updateTime2": "Update Time", "deleteFile": "Delete File", "noPermissionDelete": "You don't have permission to delete", "deleteFileSuccess": "File deleted successfully", "deleteFileFailed": "Failed to delete file", "batchDeleteSuccess": "Batch delete successful, {count} files deleted", "batchDeleteFailed": "Batch delete failed", "cannotParseWhileCancelling": "Cannot parse while cancelling", "stopParse": "Stop Parse", "retryParse": "Retry Parse", "parseFile": "Parse File"}, "status": {"success": "Success", "failed": "Failed", "parsing": "Parsing", "none": "Unprocessed", "unknown": "Unknown Status", "parseSuccess": "Parse Success", "parseFailed": "Parse Failed", "queueing": "Queueing", "parsingProgress": "Parsing ({percent})", "unparsed": "Unparsed", "cancelling": "Cancelling", "cancelled": "<PERSON><PERSON>ed"}, "operations": {"start": "Start", "stop": "Stop", "delete": "Delete", "settings": "Settings", "refresh": "Refresh"}, "messages": {"createSuccess": "Knowledge base created successfully", "createFailed": "Failed to create knowledge base", "updateSuccess": "Knowledge base updated successfully", "updateFailed": "Failed to update knowledge base", "deleteSuccess": "Knowledge base deleted successfully", "deleteFailed": "Failed to delete knowledge base", "addFilesSuccess": "Files added successfully", "addFilesFailed": "Failed to add files", "deleteFileSuccess": "File deleted successfully", "deleteFileFailed": "Failed to delete file", "batchDeleteSuccess": "Batch delete successful", "batchDeleteFailed": "Batch delete failed", "startParseSuccess": "Parse task started", "startParseFailed": "Failed to start parsing", "stopParseSuccess": "Parse task stopped", "stopParseFailed": "Failed to stop parsing", "batchStartSuccess": "Batch start successful", "batchStartFailed": "Batch start failed", "batchStopSuccess": "Batch stop successful", "batchStopFailed": "Batch stop failed"}, "deleteDialog": {"title": "Confirm Delete", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "description": "This action cannot be undone."}, "chunkMethod": {"title": "Chunk Method Settings", "currentMethod": "Current chunk method: {method}", "selectNewMethod": "Select new chunk method", "save": "Save", "cancel": "Cancel"}}, "datasets": {"title": "Dataset Management", "fileDataset": "File Dataset", "databaseDataset": "Database Dataset", "addFileDataset": "Add File Dataset", "addDatabaseDataset": "Add Database Dataset", "loading": "Loading...", "loadError": "Failed to load dataset list: {error}", "retry": "Retry", "unknownError": "Unknown error", "create": "Create Dataset", "saveChanges": "Save Changes", "emptyMessage": "No datasets yet, click the button above to create your first dataset", "comingSoon": "This feature is coming soon, please stay tuned~", "form": {"name": "Dataset Name", "nameRequired": "Dataset name is required", "description": "Dataset Description", "descriptionRequired": "Dataset description is required", "username": "Username", "usernameRequired": "Username is required", "password": "Password", "passwordRequired": "Password is required", "database": "Database Name", "databaseRequired": "Database name is required", "testConnection": "Test Connection", "connectionSuccess": "Connection successful! Database is accessible.", "connectionError": "Connection error occurred, please try again later.", "testing": "Testing...", "mustTestFirst": "Please test the connection successfully before saving", "create": "Create Dataset", "update": "Update Dataset"}, "messages": {"createSuccess": "Created Successfully", "datasetCreated": "Dataset created", "createFailed": "Creation Failed", "createError": "Error occurred while creating dataset", "updateSuccess": "Updated Successfully", "datasetUpdated": "Dataset updated", "updateFailed": "Update Failed", "updateError": "Error occurred while updating dataset", "deleteSuccess": "Deleted Successfully", "fileDatasetDeleted": "File dataset deleted successfully", "databaseDatasetDeleted": "Database dataset deleted successfully", "deleteFailed": "Deletion Failed", "deleteFileError": "Error occurred while deleting file dataset", "deleteDatabaseError": "Error occurred while deleting database dataset", "buildSuccess": "Building", "buildStarted": "Dataset build started", "buildFailed": "Build Failed", "buildError": "Error occurred while building dataset"}, "card": {"edit": "Edit", "delete": "Delete", "build": "Build", "building": "Building", "description": "Description:", "database": "Database:", "currentVersion": "Current Data Version:", "buildingVersion": "Building Version:", "createTime": "Create Time:", "creator": "Creator:", "none": "None", "noPermissionDelete": "You don't have permission to delete"}, "delete": {"title": "Confirm Delete", "description": "If the dataset is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone."}, "fileDetail": {"title": "File Details", "notFound": "Specified file not found", "loading": "Loading...", "backToDatasets": "Dataset List", "metadataConfig": "Metadata Configuration", "fileName": "File Name", "fileSize": "File Size", "status": "Status", "progress": "Progress", "updateTime": "Update Time", "actions": "Actions", "addFiles": "Add Files", "batchDelete": "<PERSON><PERSON> Delete", "batchStart": "<PERSON><PERSON> Start", "batchStop": "Batch Stop", "batchReset": "<PERSON><PERSON>", "selectedFilesCount": "Selected {count} items", "deleteFile": "Delete File", "resetParse": "Reset Parse", "startParse": "Start Parse", "stopParse": "Stop Parse", "configMetadata": "Config <PERSON>", "noPermissionDelete": "You don't have permission to delete", "noPermissionConfig": "You don't have permission to configure", "file": "File", "worksheets": "Worksheets", "worksheet": "Worksheet", "subtable": "Subtable", "buildDataset": "Build Dataset", "saveConfig": "Save Configuration", "hasUnsavedChanges": "Has unsaved changes", "configNote": "Data is cached locally during configuration, please do not refresh the page", "createdBy": "Created By", "createTime2": "Create Time", "updatedBy": "Updated By"}, "deleteDialog": {"title": "Confirm Delete", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "description": "This action cannot be undone."}, "resetDialog": {"title": "Confirm Reset Parse", "singleFile": "Are you sure you want to reset the parse for file \"{fileName}\"?", "multipleFiles": "Are you sure you want to reset the parse for the selected {count} files?", "description": "After reset, re-parsing will be required."}, "tabs": {"fileDataset": "File Dataset", "databaseDataset": "Database Dataset"}, "fileForm": {"name": "Dataset Name", "nameRequired": "Dataset name is required", "namePlaceholder": "Enter dataset name", "description": "Dataset Description", "descriptionRequired": "Dataset description is required", "descriptionPlaceholder": "Enter dataset description", "supportInfo": "Only supports standard row-column structured CSV / EXCEL files. Complex format EXCEL files may fail to parse or parse successfully but query inaccurately", "exampleLabel": "Example:", "save": "Save", "create": "Create Dataset", "update": "Update Dataset"}}, "password": {"changeTitle": "Change Account Password", "changeDescription": "Please enter a new password to complete the change", "newPassword": "New Password", "confirmPassword": "Confirm Password", "newPasswordPlaceholder": "Enter new password", "confirmPasswordPlaceholder": "Re-enter new password", "cancel": "Cancel", "confirm": "Confirm Change", "submitting": "Submitting...", "changeFailed": "Failed to change password, please try again", "changeSuccess": "Password changed successfully! Dialog will close automatically...", "validation": {"newPasswordRequired": "New password requires at least 6 characters", "confirmPasswordRequired": "Confirm password requires at least 6 characters", "passwordNotMatch": "New password and confirm password do not match"}}, "home": {"welcomeTitle": "Welcome to {siteName}", "welcomeBack": "Welcome Back", "description": " is an AI service centered on personal and enterprise datasets, designed to unleash the full potential of data.", "adminDescription": " As a team administrator, you can manage team members and configure models.", "userDescription": " You can access team-shared resources and create personal resources."}, "fileManagement": {"title": "File Management", "fileList": "File List", "filesCount": "{count} files", "searchPlaceholder": "Search files...", "selectedFiles": "Selected {count} files", "batchParse": "<PERSON><PERSON> Parse", "noFilesFound": "No matching files found", "fileName": "File Name", "parseStatus": "Parse Status", "actions": "Actions", "status": {"success": "Parse Success", "failed": "Parse Failed", "parsing": "Parsing", "none": "Not Parsed"}, "parseButton": {"parse": "Parse", "reparse": "Re-parse"}}, "forms": {"placeholders": {"username": "Enter username", "password": "Enter password", "datasetName": "Enter dataset name", "datasetDescription": "Enter dataset description", "database": "Enter database", "knowledgeBaseName": "Enter knowledge base name", "knowledgeBaseDescription": "Enter knowledge base description", "description": "Enter description...", "search": "Search...", "folderName": "Enter folder name", "searchMembers": "Search members...", "searchFiles": "Search files..."}, "selectOptions": {"pdfParser": "Select PDF Parser", "sliceMethod": "Select slice method", "defaultSliceMethod": "Select default document slice method"}}, "sliceMethod": {"naive": {"description": "Default slice size is 512, users can set each file individually"}}, "sidebar": {"projects": {"files": "Files", "knowledgeBase": "Knowledge Base", "datasets": "Datasets", "teamManagement": "Team Management", "modelConfig": "Model Configuration"}, "agent": {"addAgent": "Add Agent", "agentList": "Agent List"}, "user": {"personalSpace": "Personal Space", "personalSpaceSuffix": "'s Personal Space", "spaceSuffix": "'s Space"}}, "files": {"title": "File Management", "actions": {"uploadFile": "Upload File", "newItem": "New", "createFolder": "Create Folder", "renameFile": "Rename File", "deleteFile": "Delete File", "moveFile": "Move File", "noPermissionDelete": "You don't have permission to delete", "selectFiles": "Select Files", "clearFiles": "Clear", "startUpload": "Start Upload", "retryUpload": "Retry Upload", "retry": "Retry", "confirm": "Confirm", "cancel": "Cancel"}, "status": {"uploadSuccess": "Upload Success", "uploadFailed": "Upload Failed", "uploading": "Uploading", "waitingUpload": "Waiting Upload", "userCancel": "User Cancel"}, "messages": {"createFolderSuccess": "Created Successfully", "createFolderSuccessDesc": "Folder {folderName} has been created", "createFolderFailed": "Creation Failed", "createFolderFailedDesc": "Error occurred while creating folder", "renameFolderSuccess": "Renamed Successfully", "renameFolderSuccessDesc": "File has been renamed to {newName}", "renameFolderFailed": "Rename Failed", "renameFolderFailedDesc": "Error occurred while renaming file", "deleteSuccess": "Deleted Successfully", "deleteSuccessDesc": "File has been deleted successfully", "deleteFailed": "Deletion Failed", "deleteFailedDesc": "Error occurred while deleting file", "uploadSuccessCount": "Uploaded {count} files successfully", "filesSuccessCount": "{count} files uploaded successfully"}, "table": {"name": "Name", "type": "Type", "size": "Size", "linkedCount": "Linked Count", "createTime": "Create Time", "creator": "Creator", "updateTime": "Update Time", "updater": "Updater", "actions": "Actions"}, "upload": {"title": "Upload Files", "supportedFormats": "Supported formats: {formats}", "fileLimits": "Maximum {maxCount} files, excel, csv single file maximum {tableLimit}MB, other files single file maximum {fileLimit}MB", "selectedFiles": "Selected files ({count})", "uploadProgress": "Uploading {percent}%"}, "dialog": {"deleteTitle": "Delete File", "deleteConfirm": "Confirm Delete", "deleteCancel": "Cancel", "moveTitle": "Move {type}", "renameTitle": "Rename {type}", "renamePlaceholder": "Enter new {type} name", "folderNameRequired": "Folder name cannot be empty", "nameRequired": "Name cannot be empty"}, "types": {"file": "File", "folder": "Folder"}}}