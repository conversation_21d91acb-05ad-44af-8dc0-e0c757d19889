"use client";

import { useConfig } from "@/components/config-provider-client";
import { allExtensionAtom } from "@/store/kbase-store";
import { Alert, AlertDescription } from "@ragtop-web/ui/components/alert";
import { Badge } from "@ragtop-web/ui/components/badge";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Input } from "@ragtop-web/ui/components/input";
import { Progress } from "@ragtop-web/ui/components/progress";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useAtomValue } from "jotai";
import { AlertCircle, CheckCircle2, RefreshCw, Upload, X } from "lucide-react";
import { useEffect, useRef } from "react";
import { useFileUpload } from "../hooks/use-file-upload";
import { useFileUploadChunks } from "../hooks/use-file-upload-chunks";
import { formatFileSize, MAX_FILE_COUNT } from "../utils";

// 文件上传状态
export type FileUploadStatus = "pending" | "uploading" | "success" | "error";

// 文件上传项
export interface FileUploadItem {
  file: File;
  id: string;
  status: FileUploadStatus;
  progress: number;
  error?: string;
  abortController?: AbortController; // 取消控制器
}

// 截断文件名，保留扩展名
const truncateFileName = (fileName: string, maxLength: number = 20) => {
  const lastDotIndex = fileName.lastIndexOf(".");

  if (lastDotIndex === -1) {
    return fileName.length > maxLength ? `${fileName.slice(0, maxLength)  }...` : fileName;
  }

  const name = fileName.slice(0, lastDotIndex);
  const extension = fileName.slice(lastDotIndex);

  if (name.length > maxLength) {
    return `${name.slice(0, maxLength)  }...${  extension}`;
  }

  return fileName;
};

export function FileUpload({
  isUploadDialogOpen,
  setIsUploadDialogOpen,
  parentId,
  onUploadSuccess,
}: {
  isUploadDialogOpen: boolean;
  setIsUploadDialogOpen: (open: boolean) => void;
  parentId?: string | null;
  onUploadSuccess?: () => void;
}) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const supportedFileTypes = useAtomValue(allExtensionAtom);
  const uploadMode = useConfig().UPLOAD_MODE;
  const fileSizeLimit = useConfig().FILE_SIZE_LIMIT;
  const tableFileSizeLimit = useConfig().TABLE_FILE_SIZE_LIMIT;
  const uploadFunction = uploadMode === "client" ? useFileUploadChunks : useFileUpload;

  const {
    uploadFiles: onUploadFile,
    addFiles,
    files,
    validationErrors,
    retryFile,
    removeFile,
    clearFiles,
  } = uploadFunction({ parentId, onSuccess: onUploadSuccess });

  // 获取状态图标
  const getStatusIcon = (status: FileUploadStatus) => {
    switch (status) {
      case "success":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "uploading":
        return (
          <div className="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent" />
        );
      default:
        return null;
    }
  };

  // 获取状态文本
  const getStatusText = (file: FileUploadItem) => {
    switch (file.status) {
      case "success":
        return "上传成功";
      case "error":
        return file.error || "上传失败";
      case "uploading":
        return `上传中 ${Math.round(file.progress)}%`;
      default:
        return "等待上传";
    }
  };

  const onOpenChange = (open: boolean) => {
    if (!open) {
      files.forEach((file) => {
        if (file.status === "uploading" && file?.abortController) {
          file?.abortController.abort("用户取消");
        }
      });
      setIsUploadDialogOpen(open);
    }
  };

  const hasUploadingFiles = files.some((f) => f.status === "uploading");
  const hasErrorFiles = files.some((f) => f.status === "error");
  const hasSuccessFiles = files.some((f) => f.status === "success");
  const allFilesSuccess = files.length > 0 && files.every((f) => f.status === "success");

  useEffect(() => {
    if (allFilesSuccess) {
      const timer = setTimeout(() => {
        setIsUploadDialogOpen(false);
        toast({
          title: `上传${files.length}个文件成功`,
          variant: "default",
        });
        clearFiles();
        if (onUploadSuccess) {
          onUploadSuccess();
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [allFilesSuccess]);

  return (
    <Dialog open={isUploadDialogOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>上传文件</DialogTitle>
        </DialogHeader>
        <DialogBody className="pb-6">
          <div className="space-y-4">
            {/* 文件选择区域 */}
            <div className="border-muted-foreground/25 rounded-lg border-2 border-dashed p-2 text-center">
              <Upload className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
              <div className="space-y-2">
                <p className="text-muted-foreground text-sm">
                  支持格式如下： {supportedFileTypes.join(", ")}
                </p>
                <p className="text-muted-foreground text-xs">
                  最多可选择 {MAX_FILE_COUNT} 个文件, excel, csv 单个文件最大
                  {tableFileSizeLimit}MB, 其他文件单个文件最大 {fileSizeLimit}MB
                </p>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={hasUploadingFiles}
                >
                  选择文件
                </Button>
                <Input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept={supportedFileTypes.map((type) => `.${type}`).join(",")}
                  className="hidden"
                  onChange={addFiles}
                />
              </div>
            </div>

            {/* 验证错误提示 */}
            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* 文件列表 */}
            {files.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">已选择的文件 ({files.length})</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={clearFiles}
                    disabled={hasUploadingFiles}
                  >
                    清空
                  </Button>
                </div>

                <div className="space-y-1.5">
                  {files.map((file) => (
                    <div key={file.id} className="space-y-1.5 rounded-lg border p-2">
                      <div className="flex items-center justify-between gap-2">
                        <div className="flex min-w-0 flex-1 items-center gap-2">
                          {getStatusIcon(file.status)}
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-1">
                              <span className="truncate text-sm font-medium">
                                {truncateFileName(file.file.name)}
                              </span>
                              <span className="text-muted-foreground text-xs whitespace-nowrap">
                                ({formatFileSize(file.file.size)})
                              </span>
                            </div>
                            <div className="text-muted-foreground text-xs">
                              {getStatusText(file)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {file.status === "error" && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-7 px-2"
                              onClick={() => retryFile(file.id)}
                            >
                              <RefreshCw className="mr-1 h-3.5 w-3.5" />
                              重试
                            </Button>
                          )}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => removeFile(file.id)}
                          >
                            <X className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </div>

                      {/* 进度条 */}
                      {file.status === "uploading" && (
                        <Progress value={file.progress} className="h-1.5" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </DialogBody>
        <DialogFooter>
          {/* 操作按钮 */}
          <div className="flex justify-end gap-2">
            {!hasUploadingFiles &&
              files.some((f) => f.status === "pending" || f.status === "error") && (
                <Button type="button" onClick={() => onUploadFile(files)}>
                  {hasErrorFiles ? "重新上传" : "开始上传"}
                </Button>
              )}
            {hasSuccessFiles && !hasUploadingFiles && (
              <Badge variant="default" className="border-green-200 bg-green-50 text-green-700">
                {files.filter((f) => f.status === "success").length} 个文件上传成功
              </Badge>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
