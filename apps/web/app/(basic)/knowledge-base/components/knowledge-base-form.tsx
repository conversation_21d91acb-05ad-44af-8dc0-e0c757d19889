"use client";

import {
  <PERSON>BasePara<PERSON>,
  SliceMethodType,
  useKnowledgeBase,
} from "@/service/knowledge-base-service";
import { parseMethodAtom } from "@/store/kbase-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { useAtomValue } from "jotai";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { SliceMethodDescription, sliceMethodOptions } from "./slice-method-description";

// 定义模式创建函数，以便访问翻译
const createFormSchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, t("form.nameRequired")),
    description: z.string().optional(),
    parser_provider_id: z.literal("DeepDOC"),
    chunk_provider_id: z.string().min(1, t("form.sliceMethodRequired")),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface KnowledgeBaseFormProps {
  knowledgeBase?: KnowledgeBaseParams;
  onSave: (knowledgeBase: KnowledgeBaseParams) => void;
  isCreating: boolean;
  isLoading: boolean;
}

export function KnowledgeBaseForm({
  knowledgeBase,
  onSave,
  isCreating,
  isLoading,
}: KnowledgeBaseFormProps) {
  const t = useTranslations("knowledgeBase");
  const tForm = useTranslations("forms.placeholders");
  const tSelect = useTranslations("forms.selectOptions");
  const [selectedMethod, setSelectedMethod] = useState<SliceMethodType | undefined>(
    (knowledgeBase?.chunk_provider_id as SliceMethodType) || "naive"
  );
  const parseMethod = useAtomValue(parseMethodAtom);

  const knowledgeId = knowledgeBase?.id;

  // 获取知识库详情
  const { data, isLoading: detailLoading } = useKnowledgeBase(knowledgeId, true);

  // 创建表单验证模式
  const formSchema = useMemo(() => createFormSchema(t), [t]);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: data
      ? {
          ...data,
          parser_provider_id: "DeepDOC", // 确保始终使用 DeepDOC
          chunk_provider_id: data?.chunk_config?.chunk_provider_id ?? "naive",
        }
      : {
          name: "",
          description: "",
          parser_provider_id: "DeepDOC",
          chunk_provider_id: "naive",
        },
  });

  const supportedParseModels = new Set(parseMethod?.map((item) => item.parse_model));

  const filteredOptions = sliceMethodOptions.filter((option) =>
    supportedParseModels.has(option.value)
  );

  // 监听切片方法变化
  const watchSliceMethod = form.watch("chunk_provider_id") as SliceMethodType;

  useEffect(() => {
    if (data) {
      form.reset({
        ...data,
        parser_provider_id: "DeepDOC",
        chunk_provider_id: data?.chunk_config?.chunk_provider_id ?? "naive",
      });
    }
  }, [data]);

  useEffect(() => {
    if (watchSliceMethod) {
      setSelectedMethod(watchSliceMethod);
    }
  }, [watchSliceMethod]);

  // 保存知识库
  const handleSubmit = (values: FormValues) => {
    if (isLoading) {return;}
    onSave({
      id: knowledgeId || undefined,
      name: values.name,
      description: values?.description,
      chunk_config: {
        chunk_provider_id: values.chunk_provider_id as SliceMethodType,
      },
      pdf_parser_config: {
        parser_provider_id: "DeepDOC",
      },
      files: knowledgeBase?.files || undefined,
    });
  };

  if (!isCreating && detailLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" strokeWidth={1.5} />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.name")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={tForm("knowledgeBaseName")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          rules={{ required: false }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("form.description")}</FormLabel>
              <FormControl>
                <Input placeholder={tForm("knowledgeBaseDescription")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="parser_provider_id"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>PDF解析器</RequiredFormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue="DeepDOC"
                disabled
              >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={tSelect('pdfParser')}>DeepDOC</SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="DeepDOC">DeepDOC</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                使用 DeepDOC 解析 PDF 文档
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        /> */}

        <FormField
          control={form.control}
          name="chunk_provider_id"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.defaultSliceMethod")}</RequiredFormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={tSelect("defaultSliceMethod")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {filteredOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>{t("form.sliceMethodDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 切片方法说明 */}
        {selectedMethod && (
          <div className="bg-muted/30 rounded-md border p-3">
            {/* <h4 className="font-medium mb-2">方法说明</h4> */}
            <SliceMethodDescription method={selectedMethod} />
          </div>
        )}
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isCreating ? t("create") : t("saveChanges")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
