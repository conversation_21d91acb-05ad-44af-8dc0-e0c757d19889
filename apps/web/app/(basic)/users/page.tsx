"use client";

import { useIsTeamAdmin } from "@/lib/user-role";
import { useMemberCreate, useMemberDelete, useMembers, UserList } from "@/service/team-service";
import { Badge } from "@ragtop-web/ui/components/badge";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { type ColumnDef } from "@ragtop-web/ui/components/data-table";
import { PaginatedTable } from "@ragtop-web/ui/components/paginated-table";
import { Tooltip, TooltipContent, TooltipTrigger } from "@ragtop-web/ui/components/tooltip";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { usePagination } from "@ragtop-web/ui/hooks/use-pagination";
import { PlusIcon, ShieldCheck, Trash2, UserRound } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { AddMembersDialog } from "./components/add-members-dialog";
import { DeleteMemberDialog } from "./components/delete-member-dialog";

// 用户数据结构（用于表格展示）
interface TeamMember {
  id: string;
  name: string;
  role: "admin" | "member";
}

export default function UsersPage() {
  // 对话框状态
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null);
  const { toast } = useToast();
  const t = useTranslations("team");

  // 使用分页Hook管理分页状态
  const { pageNumber, pageSize, handlePageChange, handlePageSizeChange } = usePagination({
    initialPageSize: 10,
  });

  // API hooks
  const { data, isLoading } = useMembers(pageNumber, pageSize);
  const memberDeleteMutation = useMemberDelete();
  const memberCreateMutation = useMemberCreate();

  // 转换数据格式以适配PaginatedTable
  const transformedData = data
    ? {
        ...data,
        records: data.records.map((member: UserList) => {
          const role = member.roles?.includes("TEAM_ADMIN")
            ? ("admin" as const)
            : ("member" as const);

          return {
            id: member.id,
            name: member.user.nick || member.user.username,
            role,
          };
        }),
      }
    : undefined;

  // 处理打开添加成员对话框
  const handleOpenAddDialog = () => {
    setIsAddDialogOpen(true);
  };

  // 处理关闭添加成员对话框
  const handleCloseAddDialog = () => {
    setIsAddDialogOpen(false);
  };

  // 处理添加成员
  const handleAddMembers = async (user_ids: string[]) => {
    try {
      // 批量添加成员
      await memberCreateMutation.mutateAsync({
        user_ids,
      });

      toast({
        title: t("messages.addSuccess"),
        variant: "default",
      });
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("添加成员失败:", error);
      toast({
        title: t("messages.addFailed"),
        variant: "destructive",
      });
    }
  };

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (member: TeamMember) => {
    setMemberToDelete(member);
    setIsDeleteDialogOpen(true);
  };

  // 处理关闭删除确认对话框
  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setMemberToDelete(null);
  };

  // 处理删除成员
  const handleDeleteMember = async () => {
    if (memberToDelete) {
      try {
        await memberDeleteMutation.mutateAsync({
          member_id: memberToDelete.id,
        });

        toast({
          title: t("messages.deleteSuccess"),
          variant: "default",
        });
      } catch (error) {
        console.error("删除成员失败:", error);
        toast({
          title: t("messages.deleteFailed"),
          variant: "destructive",
        });
      }
    }
    setIsDeleteDialogOpen(false);
    setMemberToDelete(null);
  };

  // 定义表格列
  const columns: ColumnDef<TeamMember>[] = [
    {
      accessorKey: "name",
      header: t("accountName"),
      cell: ({ row }) => (
        <div className="flex items-center gap-2 font-medium">
          <UserRound className="text-muted-foreground h-4 w-4" />
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "role",
      header: t("role"),
      cell: ({ row }) => {
        const {role} = row.original;

        return role === "admin" ? (
          <Badge className="flex w-fit items-center gap-1">
            <ShieldCheck className="h-3 w-3" />
            {t("admin")}
          </Badge>
        ) : (
          <Badge variant="secondary" className="w-fit">
            {t("member")}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="w-[100px]">{t("actions")}</div>,
      cell: ({ row }) => {
        const member = row.original;

        // 只有管理员才能删除成员，且不能删除管理员
        return isAdmin && member.role !== "admin" ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={() => handleOpenDeleteDialog(member)}>
                <Trash2 className="text-destructive h-4 w-4" />
                <span className="sr-only">{t("delete")}</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>{t("deleteMemberTip")}</TooltipContent>
          </Tooltip>
        ) : null;
      },
    },
  ];

  // 检查用户是否是管理员
  const isAdmin = useIsTeamAdmin();

  return (
    <CustomContainer
      title={t("title")}
      action={
        isAdmin ? (
          <Button size="sm" onClick={handleOpenAddDialog}>
            <PlusIcon className="h-4 w-4" />
            {t("addMember")}
          </Button>
        ) : undefined
      }
    >
      <PaginatedTable
        columns={columns}
        data={transformedData}
        isLoading={isLoading}
        currentPage={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showTotal={true}
        showPageSizeSelector={true}
      />

      {/* 添加成员对话框 */}
      <AddMembersDialog
        open={isAddDialogOpen}
        onClose={handleCloseAddDialog}
        onAddMembers={handleAddMembers}
      />

      {/* 删除成员确认对话框 */}
      <DeleteMemberDialog
        open={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onDelete={handleDeleteMember}
        memberName={memberToDelete?.name || ""}
      />
    </CustomContainer>
  );
}
