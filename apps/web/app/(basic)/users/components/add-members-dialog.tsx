"use client";

import { useAvailableMembersWithDynamicPaging } from "@/service/auth-service";
import { Button } from "@ragtop-web/ui/components/button";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { SelectionList, type SelectionItem } from "@ragtop-web/ui/components/selection-list";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { usePaginatedSearch } from "@ragtop-web/ui/hooks/use-paginated-search";
import { isEmpty } from "lodash-es";
import { useTranslations } from "next-intl";
import { useState } from "react";

// 可添加的用户数据结构
interface AvailableUser {
  user_id: string;
  name: string;
  joined: boolean;
}

interface AddMembersDialogProps {
  open: boolean;
  onClose: () => void;
  onAddMembers: (user_id: string[]) => void;
}

export function AddMembersDialog({ open, onClose, onAddMembers }: AddMembersDialogProps) {
  const [selectedUserId, setSelectedUserId] = useState<string[]>([]);
  const { toast } = useToast();
  const t = useTranslations("team.addMembers");
  const tMessages = useTranslations("team.messages");
  // API hooks - 使用动态分页
  const availableMembersQuery = useAvailableMembersWithDynamicPaging();

  /**
   * 搜索函数 - 适配 usePaginatedSearch
   */
  const searchFunction = async (params: {
    pageNumber?: number;
    pageSize?: number;
    keyword?: string;
  }) => {
    try {
      const response = await availableMembersQuery.mutateAsync({
        keyword: params.keyword || undefined,
        pageNumber: params.pageNumber || 1,
        pageSize: params.pageSize || 10,
      });

      // 转换数据格式
      const formattedUsers =
        response?.records?.map((item: any) => ({
          user_id: item.user?.id || "",
          name: item.user?.nick || item.user?.username || t("unknownUser"),
          joined: item.joined,
        })) || [];

      return {
        records: formattedUsers,
        total: response?.total || 0,
      };
    } catch (error) {
      console.error("获取可添加成员失败:", error);
      toast({
        title: tMessages("fetchFailed"),
        variant: "destructive",
      });

      return { records: [], total: 0 };
    }
  };

  // 使用分页搜索 Hook
  const {
    data: availableUsers,
    isLoading,
    hasMore,
    searchTerm,
    setSearchTerm,
    handleScroll,
    reset,
  } = usePaginatedSearch<AvailableUser>({
    searchFn: searchFunction,
    pageSize: 10,
    debounceDelay: 300,
    enabled: open,
  });

  // 转换数据为 SelectionItem 格式
  const selectionItems: SelectionItem[] = availableUsers.map((user) => ({
    id: user.user_id,
    label: user.name,
    disabled: user.joined, // joined 为 true 时禁用选择
  }));

  // 处理选择变化
  const handleSelectionChange = (selectedIds: string | string[]) => {
    if (Array.isArray(selectedIds)) {
      setSelectedUserId(selectedIds);
    }
  };

  // 处理添加成员
  const handleAddMembers = () => {
    onAddMembers(selectedUserId);
    handleClose();
  };

  // 处理关闭对话框
  const handleClose = () => {
    setSelectedUserId([]);
    reset(); // 重置分页搜索状态
    onClose();
  };

  return (
    <CustomDrawer
      open={open}
      onClose={handleClose}
      title={t("title")}
      footer={
        <Button onClick={handleAddMembers} disabled={selectedUserId.length === 0}>
          {t("addButton")}
        </Button>
      }
    >
      <div className="space-y-4">
        <p className="text-muted-foreground text-sm">{t("description")}</p>

        <SelectionList
          items={selectionItems}
          isLoading={isLoading}
          hasMore={hasMore}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onScroll={handleScroll}
          selectedIds={selectedUserId}
          onSelectionChange={handleSelectionChange}
          searchPlaceholder={t("searchPlaceholder")}
          emptyText={t("emptyText")}
          loadingText={t("loadingText")}
          endText={t("endText")}
          height="300px"
        />

        {/* 已选择提示 */}
        {!isEmpty(selectedUserId) && (
          <div className="text-muted-foreground text-sm">
            {t("selectedCount", { count: selectedUserId.length })}
          </div>
        )}
      </div>
    </CustomDrawer>
  );
}
