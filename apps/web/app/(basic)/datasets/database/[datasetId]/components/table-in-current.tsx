"use client";

import { type DbTableColumn } from "@/service/dataset-service";
import { Button } from "@ragtop-web/ui/components/button";
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table";
import { Pencil } from "lucide-react";
import { useMemo, useState } from "react";

import { Tooltip, TooltipContent, TooltipTrigger } from "@ragtop-web/ui/components/tooltip";
import EditDialog from "./edit-dialog";

export default function TableInCurrent({
  schemaLists,
  onTableSelect,
  columnLists,
  tableLists,
  setColumnLists,
  onSchemaSelect,
  originalParams: upParams,
}: {
  schemaLists?: DbTableColumn[];
  onTableSelect: any;
  columnLists?: DbTableColumn[];
  tableLists?: DbTableColumn[];
  setColumnLists?: any;
  onSchemaSelect?: any;
  originalParams: {
    column_name?: string;
    schema_name?: string;
    table_name?: string;
    tableset_id?: string;
    annotation?: string;
  };
}) {
  const [originalParams, setOriginalParams] = useState<DbTableColumn | undefined>(undefined);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // 处理编辑文档
  const handleEditDocument = (document: DbTableColumn) => {
    if (document?.schema_name) {
      const { schema_name, annotation } = document;

      setOriginalParams({ ...upParams, schema_name, annotation });
    } else if (document?.table_name) {
      const { table_name, annotation } = document;

      setOriginalParams({ ...upParams, table_name, annotation });
    } else {
      const { column_name, annotation } = document;

      setOriginalParams({ ...upParams, column_name, annotation });
    }

    setIsEditDialogOpen(true);
  };

  const schemaColumns: ColumnDef<DbTableColumn>[] = [
    {
      accessorKey: "schema_name",
      header: "Schema Name",
      cell: ({ row }) => {
        const document = row.original;

        return (
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSchemaSelect({ ...upParams, ...document, expanded: true })}
            >
              {document.schema_name}
            </Button>
          </div>
        );
      },
    },
    {
      accessorKey: "annotation",
      header: "Annotation",
      cell: ({ row }) => {
        const document = row.original;

        return (
          <div className="flex items-center justify-between">
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[200px] truncate">{document.annotation || "-"}</div>
              </TooltipTrigger>
              <TooltipContent side="left" className="max-h-[400px] overflow-y-auto">
                <p className="max-w-[300px] break-words whitespace-normal">
                  {document.annotation || "-"}
                </p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" onClick={() => handleEditDocument(document)}>
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">编辑</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>编辑Annotation</p>
              </TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const tableColumns: ColumnDef<DbTableColumn>[] = [
    {
      accessorKey: "table_name",
      header: "Table Name",
      cell: ({ row }) => {
        const document = row.original;

        return (
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onTableSelect({ ...upParams, ...document })}
            >
              {document.table_name}
            </Button>
          </div>
        );
      },
    },
    {
      accessorKey: "annotation",
      header: "Annotation",
      cell: ({ row }) => {
        const document = row.original;

        return (
          <div className="flex items-center justify-between">
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[200px] truncate">{document.annotation || "-"}</div>
              </TooltipTrigger>
              <TooltipContent side="left" className="max-h-[400px] overflow-y-auto">
                <p className="max-w-[300px] break-words whitespace-normal">
                  {document.annotation || "-"}
                </p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" onClick={() => handleEditDocument(document)}>
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">编辑</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>编辑Annotation</p>
              </TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // 表格列定义
  const columns: ColumnDef<DbTableColumn>[] = [
    {
      accessorKey: "column_name",
      header: "Column Name",
    },
    {
      accessorKey: "data_type",
      header: "Column Type",
    },
    {
      accessorKey: "annotation",
      header: "Annotation",
      cell: ({ row }) => {
        const document = row.original;

        return (
          <div className="flex items-center justify-between">
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[200px] truncate">{document.annotation || "-"}</div>
              </TooltipTrigger>
              <TooltipContent side="left" className="max-h-[400px] overflow-y-auto">
                <p className="max-w-[300px] break-words whitespace-normal">
                  {document.annotation || "-"}
                </p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" onClick={() => handleEditDocument(document)}>
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">编辑</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>编辑Annotation</p>
              </TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const final = useMemo(() => {
    if (!upParams?.schema_name) {
      return {
        columns: schemaColumns,
        data: schemaLists,
      };
    }
    if (!upParams?.table_name) {
      return {
        columns: tableColumns,
        data: tableLists,
      };
    }

    return {
      columns,
      data: columnLists,
    };
  }, [upParams, schemaLists, tableLists, columnLists]);

  return (
    <>
      {/* 文档表格 */}
      <div className="h-full overflow-hidden">
        <DataTable columns={final.columns} data={final.data || []} />
      </div>
      {isEditDialogOpen && (
        <EditDialog
          open={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          originalParams={originalParams}
          setColumnLists={setColumnLists}
        />
      )}
    </>
  );
}
