"use client";

import { formatDateTime } from "@/lib/utils";
import {
  useBuildDataset,
  useDataset,
  useDatasetDocuments,
  useDatasetSchemasInfinite,
  type DbTableColumn,
} from "@/service/dataset-service";
import { Badge } from "@ragtop-web/ui/components/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@ragtop-web/ui/components/breadcrumb";
import { Button } from "@ragtop-web/ui/components/button";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { Database, Loader2, Play, Table } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import SchemaList from "./components/schema-list";
import TableInCurrent from "./components/table-in-current";

interface TableNode {
  name?: string;
  schema_name?: string;
  table_name?: string;
  tableset_id?: string;
  [property: string]: any;
}

export default function DatasetDetails({ datasetId }: { datasetId: string }) {
  const { toast } = useToast();
  const router = useRouter();
  const [isInitialized, setIsInitialized] = useState(false);
  const [originalParams, setOriginalParams] = useState<TableNode | null>({
    tableset_id: datasetId,
  });
  // const [schemaLists, setSchemaLists] = useState<any[]>([]);
  const [tableLists, setTableLists] = useState<any[]>([]);
  const [columnLists, setColumnLists] = useState<DbTableColumn[]>([]);
  const [expandedTables, setExpandedTables] = useState<string[]>([]);

  const [breadcrumbs, setBreadcrumbs] = useState<Array<{ name: string; type: "schema" | "table" }>>(
    [{ name: "Schema", type: "schema" }]
  );

  // API hooks - 延迟加载
  const { data: dataset, isError } = useDataset(datasetId, isInitialized);

  const getDocumentsMutation = useDatasetDocuments();
  const buildDatasetMutation = useBuildDataset();

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isError) {
      router.push("/");
    }
  }, [isError]);

  const { data: schemaLists } = useDatasetSchemasInfinite(datasetId, 10);

  const handleSchemaClick = (schema: TableNode) => {
    setOriginalParams(schema);
    setBreadcrumbs([
      { name: "Schema", type: "schema" },
      { name: schema?.schema_name || "", type: "schema" },
    ]);
    if (schema.expanded && !expandedTables.includes(schema?.schema_name || "")) {
      setExpandedTables([...expandedTables, schema.schema_name || ""]);
    }
  };

  // 处理列选择
  const handleSchemaSelect = useCallback(
    (schema: TableNode) => {
      setOriginalParams(schema);
      setBreadcrumbs([
        { name: "Schema", type: "schema" },
        { name: schema?.schema_name || "", type: "schema" },
      ]);
      // 如果 schema 包含 expanded 标记，则保持展开状态
      if (schema.expanded && !expandedTables.includes(schema?.schema_name || "")) {
        setExpandedTables([...expandedTables, schema.schema_name || ""]);
      }
    },
    [expandedTables]
  );

  const handleTableList = (tableList: any[]) => {
    setTableLists(tableList);
  };

  // 处理表格选择
  const handleTableSelect = (table: TableNode) => {
    setOriginalParams(table);
    setBreadcrumbs([
      { name: "Schema", type: "schema" },
      { name: table?.schema_name || "", type: "schema" },
      { name: table?.table_name || "", type: "table" },
    ]);

    // 获取表格的列数据
    getDocumentsMutation.mutate(
      {
        tableset_id: datasetId,
        schema_name: table.schema_name,
        table_name: table.table_name,
      },
      {
        onSuccess: (data) => {
          setColumnLists(data);
        },
        onError: () => {
          toast({
            title: "错误",
            description: "获取表格列数据失败",
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理构建数据集
  const handleBuildDataset = () => {
    if (!dataset?.id) {return;}

    buildDatasetMutation.mutate(
      { tableset_id: dataset.id },
      {
        onSuccess: () => {
          toast({
            title: "构建中",
            description: "数据集已开始构建",
          });
        },
        onError: () => {
          toast({
            title: "构建失败",
            description: "构建数据集时发生错误",
            variant: "destructive",
          });
        },
      }
    );
  };

  if (isError) {
    return (
      <CustomContainer title="数据集详情">
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">未找到指定的数据集</p>
        </div>
      </CustomContainer>
    );
  }

  if (!dataset) {
    return (
      <CustomContainer title="数据集详情">
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </CustomContainer>
    );
  }

  // 构建面包屑导航
  const containerBreadcrumbs = [
    {
      title: "数据集列表",
      href: "/datasets",
      isCurrent: false,
      linkComponent: Link,
    },
    {
      title: dataset.name || "数据集详情",
      href: `/datasets/${datasetId}`,
      isCurrent: true,
    },
  ];

  return (
    <CustomContainer
      title={dataset.name || "数据集详情"}
      breadcrumbs={containerBreadcrumbs}
      action={
        <Button
          size="sm"
          onClick={handleBuildDataset}
          disabled={buildDatasetMutation.isPending || !!dataset.building_version}
        >
          {dataset.building_version ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              构建中
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              构建
            </>
          )}
        </Button>
      }
    >
      {/* 数据集信息 */}
      <div className="text-muted-foreground bg-muted/30 mb-2 flex items-center gap-6 rounded-lg p-4 text-sm">
        <div className="flex items-center gap-2">
          <span className="font-medium">创建时间:</span>
          {dataset.create_time ? formatDateTime(new Date(dataset.create_time)) : ""}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">数据库:</span>
          {dataset.meta?.database || "无"}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">当前数据版本:</span>
          {dataset.build_version || "无"}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">创建人:</span>
          {dataset.creator?.name}
        </div>
        {dataset.building_version && (
          <div className="flex items-center gap-2">
            <span className="font-medium">构建中版本:</span>
            <Badge variant="outline">{dataset.building_version}</Badge>
          </div>
        )}
      </div>

      {/* 左右布局 */}
      <div className="grid h-[calc(100vh-300px)] grid-cols-12 gap-6">
        {/* 左侧树形结构 */}
        <SchemaList
          datasetId={datasetId}
          onTableSelect={handleTableSelect}
          onTableList={handleTableList}
          originalParams={originalParams}
          onSchemaSelect={handleSchemaSelect}
          expandedTables={expandedTables}
          setExpandedTables={setExpandedTables}
        />

        {/* 右侧内容区域 */}
        <div className="col-span-9 h-[calc(100vh-300px)] space-y-4">
          {originalParams ? (
            <>
              {/* 面包屑导航 */}
              <Breadcrumb className="mb-4">
                <BreadcrumbList>
                  {breadcrumbs.map((item, index) => (
                    <div key={item.name} className="flex items-center">
                      {index > 0 && <BreadcrumbSeparator />}
                      <BreadcrumbItem>
                        <BreadcrumbLink
                          asChild
                          onClick={() => {
                            if (index === 0) {
                              setOriginalParams((prev) => {
                                const updatedParams = {
                                  ...prev,
                                  schema_name: undefined,
                                  table_name: undefined,
                                };

                                return prev ? updatedParams : null;
                              });
                              setBreadcrumbs([{ name: "Schema", type: "schema" }]);
                            } else if (index === 1) {
                              setOriginalParams((prev) => {
                                const updatedParams = {
                                  ...prev,
                                  table_name: undefined,
                                };

                                return prev ? updatedParams : null;
                              });
                              setBreadcrumbs([
                                { name: "Schema", type: "schema" },
                                { name: item.name, type: "schema" },
                              ]);
                            }
                          }}
                        >
                          <div>{item.name}</div>
                        </BreadcrumbLink>
                      </BreadcrumbItem>
                    </div>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>

              {/* 表格标题 */}
              <div className="flex items-center gap-2">
                <Table className="text-primary h-4 w-4" />
                <h2 className="text-lg font-medium">
                  {originalParams?.table_name || originalParams?.schema_name || "Schema"}
                </h2>
              </div>

              {/* 文档表格 */}
              {getDocumentsMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin opacity-70" />
              ) : (
                <TableInCurrent
                  schemaLists={schemaLists}
                  columnLists={columnLists}
                  onTableSelect={handleTableSelect}
                  tableLists={tableLists}
                  originalParams={originalParams}
                  onSchemaSelect={handleSchemaClick}
                  setColumnLists={setColumnLists}
                />
              )}
            </>
          ) : (
            <div className="text-muted-foreground flex h-64 items-center justify-center">
              <div className="text-center">
                <Database className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>请从左侧选择一个表格查看列数据</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </CustomContainer>
  );
}
