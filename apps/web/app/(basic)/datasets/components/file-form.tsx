"use client";

import { type Dataset } from "@/service/dataset-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ragtop-web/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(1, "数据集名称不能为空"),
  description: z.string().min(1, "数据集描述不能为空"),
});

type FormValues = z.infer<typeof formSchema>;

interface DatasetFormProps {
  dataset?: Dataset;
  onSave: (formData: FormValues) => void;
  isCreating: boolean;
  isLoading: boolean;
}

export function FilesetForm({ dataset, onSave, isLoading }: DatasetFormProps) {
  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: dataset
      ? {
          name: dataset.name || "",
          description: dataset.description || "",
        }
      : {
          name: "",
          description: "",
        },
  });

  // 保存数据集
  const handleSubmit = async () => {
    const values = form.getValues();

    try {
      onSave(values);
    } catch (e) {
      console.log(e);
    }
  };

  const onInvalid = (errors: any) => console.error(errors);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit, onInvalid)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>数据集名称</RequiredFormLabel>
              <FormControl>
                <Input placeholder="输入数据集名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>数据集描述</RequiredFormLabel>
              <FormControl>
                <Input placeholder="输入数据集描述" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <p className="text-muted-foreground text-sm">
            仅支持标准行列结构的CSV / EXCEL文件, 复杂格式的EXCEL文件会解析失败或解析成功查询不准确
          </p>
          <span className="text-muted-foreground text-sm">示例：</span>
          <Image src="/template/csv-template.png" alt="excel" width={410} height={350} />
        </div>

        <div className="flex gap-3">
          <Button type="submit" className="flex-1" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            保存
          </Button>
        </div>
      </form>
    </Form>
  );
}
