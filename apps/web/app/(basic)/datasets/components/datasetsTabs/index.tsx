"use client";

import { useConfig } from "@/components/config-provider-client";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { cn } from "@ragtop-web/ui/lib/utils";
import { Bell } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useTranslations } from "next-intl";
import React, { useEffect, useRef, useState } from "react";
import Dbsets from "../dbsets";
import Filesets from "../filesets";

interface Tab {
  label: string;
  value: string;
  subRoutes?: string[];
}

interface AnimatedTabsProps {
  tabs: Tab[];
}

export function useTabs({
  tabs,
  initialTabId,
  onChange,
}: {
  tabs: Tab[];
  initialTabId: string;
  onChange?: (id: string) => void;
}) {
  const [[selectedTabIndex, direction], setSelectedTab] = useState(() => {
    const indexOfInitialTab = tabs.findIndex((tab) => tab.value === initialTabId);

    return [indexOfInitialTab === -1 ? 0 : indexOfInitialTab, 0];
  });

  return {
    tabProps: {
      tabs,
      selectedTabIndex,
      onChange,
      setSelectedTab,
    },
    selectedTab: tabs[selectedTabIndex] || tabs[0],
    contentProps: {
      direction,
      selectedTabIndex,
    },
  };
}

const transition = {
  type: "tween",
  ease: "easeOut",
  duration: 0.15,
};

const getHoverAnimationProps = (hoveredRect: DOMRect, navRect: DOMRect) => ({
  x: hoveredRect.left - navRect.left - 10,
  y: hoveredRect.top - navRect.top - 4,
  width: hoveredRect.width + 20,
  height: hoveredRect.height + 10,
});

const TabContent = ({ tab }: { tab: Tab }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={transition}
      className="mt-4 rounded-lg"
    >
      {tab.value === "dbsets" && <Dbsets />}
      {tab.value === "filesets" && <Filesets />}
    </motion.div>
  );
};

const Tabs = ({
  tabs,
  selectedTabIndex,
  setSelectedTab,
}: {
  tabs: Tab[];
  selectedTabIndex: number;
  setSelectedTab: (input: [number, number]) => void;
}): React.ReactElement => {
  const { toast } = useToast();
  const t = useTranslations("datasets");
  const shouldNotShowNL2Code = useConfig().SHOW_NL2CODE === "false";
  const [buttonRefs, setButtonRefs] = useState<Array<HTMLButtonElement | null>>([]);
  const navRef = useRef<HTMLDivElement>(null);
  const navRect = navRef.current?.getBoundingClientRect();
  const selectedRect = buttonRefs[selectedTabIndex]?.getBoundingClientRect();
  const [hoveredTabIndex, setHoveredTabIndex] = useState<number | null>(null);
  const hoveredRect = buttonRefs[hoveredTabIndex ?? -1]?.getBoundingClientRect();

  useEffect(() => {
    setButtonRefs((prev) => prev.slice(0, tabs.length));
  }, [tabs.length]);

  return (
    <nav
      ref={navRef}
      className="relative z-0 flex flex-shrink-0 items-center justify-center py-2"
      onPointerLeave={() => setHoveredTabIndex(null)}
    >
      {tabs.map((item, i) => {
        const isActive = selectedTabIndex === i;

        return (
          <button
            key={item.value}
            className="relative z-20 flex h-8 cursor-pointer items-center rounded-md bg-transparent px-4 text-sm transition-colors select-none"
            onPointerEnter={() => setHoveredTabIndex(i)}
            onFocus={() => setHoveredTabIndex(i)}
            onClick={() => {
              if (shouldNotShowNL2Code && item.value === "filesets") {
                toast({
                  icon: <Bell className="h-4 w-4" />,
                  title: t("comingSoon"),
                  variant: "default",
                });

                return;
              }

              setSelectedTab([i, i > selectedTabIndex ? 1 : -1]);
            }}
          >
            <motion.span
              ref={(el) => {
                buttonRefs[i] = el as HTMLButtonElement;
              }}
              className={cn("block", {
                "text-zinc-500": !isActive,
                "font-medium text-black dark:text-white": isActive,
              })}
            >
              <span className={item.value === "danger-zone" ? "text-red-500" : ""}>
                {item.label}
              </span>
            </motion.span>
          </button>
        );
      })}

      <AnimatePresence>
        {hoveredRect && navRect && (
          <motion.div
            key="hover"
            className={`absolute top-0 left-0 z-10 rounded-md ${
              hoveredTabIndex === tabs.findIndex(({ value }) => value === "danger-zone")
                ? "bg-red-100 dark:bg-red-500/30"
                : "bg-zinc-100 dark:bg-zinc-800"
            }`}
            initial={{
              ...getHoverAnimationProps(hoveredRect, navRect),
              opacity: 0,
            }}
            animate={{
              ...getHoverAnimationProps(hoveredRect, navRect),
              opacity: 1,
            }}
            exit={{
              ...getHoverAnimationProps(hoveredRect, navRect),
              opacity: 0,
            }}
            transition={transition}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {selectedRect && navRect && (
          <motion.div
            className={`absolute bottom-0 left-0 z-10 h-[2px] ${
              selectedTabIndex === tabs.findIndex(({ value }) => value === "danger-zone")
                ? "bg-red-500"
                : "bg-black dark:bg-white"
            }`}
            initial={false}
            animate={{
              width: selectedRect.width + 18,
              x: `calc(${selectedRect.left - navRect.left - 9}px)`,
              opacity: 1,
            }}
            transition={transition}
          />
        )}
      </AnimatePresence>
    </nav>
  );
};

export function DatasetsTabs({ tabs }: AnimatedTabsProps) {
  const t = useTranslations("datasets.tabs");

  const [hookProps] = useState(() => {
    // 确保 tabs 数组不为空
    if (tabs.length === 0) {
      throw new Error("Tabs array cannot be empty");
    }

    const initialTabId = tabs[0]!.value;

    return {
      tabs: tabs.map(({ label, value, subRoutes }) => ({
        label:
          value === "filesets"
            ? t("fileDataset")
            : value === "dbsets"
              ? t("databaseDataset")
              : label,
        value,
        subRoutes,
      })),
      initialTabId,
    };
  });

  const framer = useTabs(hookProps);

  // 确保 selectedTab 总是有值
  if (!framer.selectedTab) {
    return null;
  }

  return (
    <div className="-mt-2 w-full">
      <div className="dark:border-dark-4 relative flex w-full items-center justify-between overflow-x-auto overflow-y-hidden border-b">
        <Tabs {...framer.tabProps} />
      </div>
      <AnimatePresence mode="wait">
        <TabContent tab={framer.selectedTab} />
      </AnimatePresence>
    </div>
  );
}
