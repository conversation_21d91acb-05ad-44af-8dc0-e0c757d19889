"use client";

import { useDatasetTablesInfinite } from "@/service/dataset-service";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";
import { cn } from "@ragtop-web/ui/lib/utils";
import { isEmpty, isEqual } from "lodash-es";
import { Table } from "lucide-react";
import { memo, useEffect, useMemo, useRef } from "react";

interface TableListProps {
  schemaName: string;
  datasetId: string;
  onTableSelect: any;
  originalParams: any;
  onTableList: any;
}

const TableList = memo(
  function TableList({
    schemaName,
    onTableSelect,
    datasetId,
    originalParams,
    onTableList,
  }: TableListProps) {
    const {
      data,
      isLoading,
      hasMore,
      isInitialLoading,
      isFetchingNextPage,
      error,
      fetchNextPage,
      reload,
    } = useDatasetTablesInfinite(schemaName, datasetId, 10);
    const prevDataRef = useRef<any[] | null>(null);

    const currentParams = useMemo(
      () => ({
        schema_name: schemaName,
        tableset_id: datasetId,
      }),
      [schemaName, datasetId]
    );

    useEffect(() => {
      if (isEmpty(data)) {return;}
      const isSame = isEqual(prevDataRef.current, data);

      if (!isSame && schemaName) {
        onTableList(data);
        prevDataRef.current = data;
      }
    }, [data, schemaName, datasetId, onTableList, originalParams]);

    return (
      <div className="ml-6 space-y-1">
        <InfiniteScrollList
          data={data}
          isLoading={isLoading}
          hasMore={hasMore}
          isInitialLoading={isInitialLoading}
          isFetchingNextPage={isFetchingNextPage}
          error={error}
          fetchNextPage={fetchNextPage}
          reload={reload}
          renderItem={(table) => (
            <div
              key={table.table_name}
              className={cn(
                "flex cursor-pointer items-center gap-2 rounded-md p-2 text-sm",
                originalParams?.table_name === table.table_name
                  ? "bg-primary/10 text-primary"
                  : "hover:bg-muted/50"
              )}
              onClick={() => onTableSelect({ ...table, ...currentParams })}
            >
              <Table className="h-4 w-4 shrink-0" />
              <span className="truncate">{table.table_name}</span>
            </div>
          )}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，只有当关键属性变化时才重新渲染
    return (
      prevProps.schemaName === nextProps.schemaName &&
      prevProps.datasetId === nextProps.datasetId &&
      prevProps.originalParams?.table_name === nextProps.originalParams?.table_name
    );
  }
);

export default TableList;
