import * as React from "react";
import { type LucideIcon } from "lucide-react";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@ragtop-web/ui/components/sidebar";
import { ModelChangeDialog } from "../model-change-dialog";

export function NavSecondary({
  items,
  ...props
}: {
  items: {
    title: string;
    icon: LucideIcon;
  }[];
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const [isModelDialogOpen, setIsModelDialogOpen] = React.useState(false);

  const handleOpenModelDialog = () => {
    setIsModelDialogOpen(true);
  };

  const handleCloseModelDialog = () => {
    setIsModelDialogOpen(false);
  };

  return (
    <>
      <SidebarGroup {...props}>
        <SidebarGroupContent>
          <SidebarMenu>
            {items.map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton size="sm" onClick={handleOpenModelDialog}>
                  <item.icon />
                  <span className="text-sm">{item.title}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <ModelChangeDialog open={isModelDialogOpen} onClose={handleCloseModelDialog} />
    </>
  );
}
