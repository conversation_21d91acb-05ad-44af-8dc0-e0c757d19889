"use client";

import {
  Sessions,
  useDeleteSession,
  useMultiDeleteSessions,
  useSessionsInfinite,
  useUpdateSession,
} from "@/service/session-service";
import { Button } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";
import { Input } from "@ragtop-web/ui/components/input";
import {
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@ragtop-web/ui/components/sidebar";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { Edit2, Trash2 } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { memo, ReactNode, useEffect, useState } from "react";
import { BatchDeleteDialog } from "../agent";

interface SessionListProps {
  agentId: string;
  isExpanded: boolean;
  isBatchDeleteMode?: boolean;
  onCancelBatchDelete?: () => void;
}

const SidebarStable = memo(function SidebarStable({ children }: { children: ReactNode }) {
  return <SidebarMenuSub className="mr-0 max-w-full min-w-0 pr-0">{children}</SidebarMenuSub>;
});

/**
 * Session列表组件
 * 显示指定Agent下的所有sessions
 */
export function SessionList({
  agentId,
  isExpanded,
  isBatchDeleteMode = false,
  onCancelBatchDelete,
}: SessionListProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<Sessions | null>(null);
  const [batchDeleteDialogOpen, setBatchDeleteDialogOpen] = useState(false);
  const [selectedSessionIds, setSelectedSessionIds] = useState<Set<string>>(new Set());

  // 当批量删除模式关闭时，清空选中的Session
  useEffect(() => {
    if (!isBatchDeleteMode) {
      setSelectedSessionIds(new Set());
    }
  }, [isBatchDeleteMode]);

  // 获取sessions数据
  const {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    fetchNextPage,
    reload,
  } = useSessionsInfinite(agentId, 10, true);

  // API hooks
  const deleteSession = useDeleteSession();
  const updateSession = useUpdateSession(agentId);
  const multiDeleteSessions = useMultiDeleteSessions();

  // 检查session是否被选中
  const isSessionSelected = (sessionId: string) => {
    return pathname === `/agent/${agentId}/${sessionId}`;
  };

  // 打开删除确认对话框
  const openDeleteDialog = (session: Sessions) => {
    setSessionToDelete(session);
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSessionToDelete(null);
  };

  // 处理删除session
  const handleDeleteSession = async () => {
    if (!sessionToDelete) {return;}

    try {
      await deleteSession.mutateAsync({ session_id: sessionToDelete.id });
      toast({
        title: "成功",
        description: "会话删除成功",
      });
      closeDeleteDialog();
      router.push("/");
    } catch (error) {
      console.error("删除会话失败:", error);
      toast({
        title: "错误",
        description: "删除会话失败",
        variant: "destructive",
      });
    }
  };

  // 开始重命名
  const startRename = (session: Sessions) => {
    setEditingSessionId(session.id);
    setEditingTitle(session.title);
  };

  // 取消重命名
  const cancelRename = () => {
    setEditingSessionId(null);
    setEditingTitle("");
  };

  // 保存重命名
  const saveRename = async (sessionId: string) => {
    if (!editingTitle.trim()) {
      toast({
        title: "错误",
        description: "会话名称不能为空",
        variant: "destructive",
      });

      return;
    }

    try {
      await updateSession.mutateAsync({
        session_id: sessionId,
        title: editingTitle.trim(),
      });
      toast({
        title: "成功",
        description: "会话重命名成功",
      });
      setEditingSessionId(null);
      setEditingTitle("");
    } catch (error) {
      console.error("重命名会话失败:", error);
      toast({
        title: "错误",
        description: "重命名会话失败",
        variant: "destructive",
      });
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent, sessionId: string) => {
    if (e.key === "Enter") {
      saveRename(sessionId);
    } else if (e.key === "Escape") {
      cancelRename();
    }
  };

  // 处理session选择
  const handleSessionSelection = (sessionId: string, checked: boolean) => {
    setSelectedSessionIds((prev) => {
      const newSelected = new Set(prev);

      if (checked) {
        newSelected.add(sessionId);
      } else {
        newSelected.delete(sessionId);
      }

      return newSelected;
    });
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // 全选 - 将所有session ID添加到选中集合
      const allSessionIds = data.map((session) => session.id);

      allSessionIds.forEach((id) => handleSessionSelection(id, true));
    } else {
      // 全不选 - 将所有session ID从选中集合中移除
      const allSessionIds = data.map((session) => session.id);

      allSessionIds.forEach((id) => handleSessionSelection(id, false));
    }
  };

  // 处理批量删除确认
  const handleBatchDeleteConfirm = () => {
    setBatchDeleteDialogOpen(true);
  };

  // 取消批量删除
  const handleCancelBatchDelete = () => {
    onCancelBatchDelete?.();
    setSelectedSessionIds(new Set());
  };

  // 取消批量删除确认
  const handleCancelBatchDeleteDialog = () => {
    setBatchDeleteDialogOpen(false);
  };

  // 如果没有展开，不渲染
  if (!isExpanded) {
    return null;
  }

  // 如果没有session数据，不显示批量删除控制界面
  const hasSessions = data && data.length > 0;

  return (
    <>
      <SidebarStable>
        {/* 批量删除模式的控制按钮 - 只有在有session时才显示 */}
        {hasSessions && isBatchDeleteMode && (
          <div className="border-border flex min-w-0 items-center justify-between border-b py-1">
            <div className="flex min-w-0 items-center gap-2">
              <Checkbox
                checked={selectedSessionIds.size > 0 && selectedSessionIds.size === data.length}
                onCheckedChange={handleSelectAll}
                className="h-4 w-4 shrink-0"
              />
              <span className="text-muted-foreground truncate text-xs">全选</span>
            </div>
            <div className="flex shrink-0 gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                onClick={handleCancelBatchDelete}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                size="sm"
                className="h-6 px-2 text-xs"
                onClick={handleBatchDeleteConfirm}
                disabled={selectedSessionIds.size === 0}
              >
                删除 ({selectedSessionIds.size})
              </Button>
            </div>
          </div>
        )}

        {/* 如果没有session，显示空状态 */}
        {!hasSessions && !isLoading && (
          <div className="px-2 py-4 text-center">
            <p className="text-muted-foreground text-xs">暂无会话</p>
          </div>
        )}

        {/* 只有在有session时才显示session列表 */}
        {hasSessions && (
          <InfiniteScrollList
            data={data}
            isLoading={isLoading}
            hasMore={hasMore}
            isInitialLoading={isInitialLoading}
            isFetchingNextPage={isFetchingNextPage}
            error={error}
            fetchNextPage={fetchNextPage}
            reload={reload}
            renderItem={(session) => (
              <SidebarMenuSubItem
                key={session.id}
                className="group/item group/sub-item relative w-full min-w-0"
              >
                {editingSessionId === session.id ? (
                  // 重命名模式
                  <div className="flex min-w-0 items-center px-2 py-1">
                    <Input
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      onKeyDown={(e) => handleKeyDown(e, session.id)}
                      onBlur={() => saveRename(session.id)}
                      className="h-6 min-w-0 text-sm"
                      autoFocus
                    />
                  </div>
                ) : (
                  // 正常显示模式
                  <div className="flex w-full min-w-0 items-center">
                    {isBatchDeleteMode && (
                      <Checkbox
                        checked={selectedSessionIds.has(session.id)}
                        onCheckedChange={(checked) => handleSessionSelection(session.id, !!checked)}
                        className="h-4 w-4"
                        onClick={(e) => e.stopPropagation()}
                      />
                    )}
                    <SidebarMenuSubButton
                      asChild
                      isActive={isSessionSelected(session.id)}
                      onDoubleClick={() => !isBatchDeleteMode && startRename(session)}
                      className={
                        `${isBatchDeleteMode ? "w-full min-w-0 flex-1" : "w-full min-w-0" 
                        } group-hover/item:bg-sidebar-accent group-hover/item:text-accent-foreground transition-colors`
                      }
                    >
                      <Link
                        href={`/agent/${agentId}/${session.id}`}
                        prefetch={false}
                        className="w-full min-w-0"
                        onClick={(e) => isBatchDeleteMode && e.preventDefault()}
                      >
                        <span
                          className={`w-full min-w-0 truncate ${isBatchDeleteMode ? "text-xs" : ""}`}
                        >
                          {session.title}
                        </span>
                      </Link>
                    </SidebarMenuSubButton>
                  </div>
                )}

                {/* 悬停时显示的操作按钮 - 仅在非批量删除模式下显示 */}
                {!isBatchDeleteMode && (
                  <div className="group-hover/sub-item:bg-sidebar-accent absolute top-1/2 right-2 flex -translate-y-1/2 gap-1 opacity-0 transition-opacity group-hover/sub-item:opacity-100">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-muted-foreground hover:text-accent-foreground h-6 w-6 rounded-none p-0.5 hover:bg-black/10"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        startRename(session);
                      }}
                      aria-label={`重命名 ${session.title}`}
                    >
                      <Edit2 className="h-3.5 w-3.5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:bg-destructive/10 hover:text-destructive h-6 w-6 rounded-none p-0.5"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        openDeleteDialog(session);
                      }}
                      aria-label={`删除 ${session.title}`}
                    >
                      <Trash2 className="h-3.5 w-3.5" color="#f26363" />
                      <span className="sr-only">删除 {session.title}</span>
                    </Button>
                  </div>
                )}
              </SidebarMenuSubItem>
            )}
            renderEmpty={() => (
              <div className="px-2 py-4 text-center">
                <p className="text-muted-foreground text-xs">暂无会话</p>
              </div>
            )}
            renderLoadDone={() => <></>}
          />
        )}
      </SidebarStable>

      {/* 单个会话删除确认对话框 - 使用 ConfirmDialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="确认删除会话"
        description={`您确定要删除会话 "${sessionToDelete?.title}" 吗？此操作无法撤销，会话中的所有消息都将被永久删除。`}
        onConfirm={handleDeleteSession}
        onCancel={closeDeleteDialog}
        confirmText="确认删除"
        cancelText="取消"
        variant="destructive"
        loading={deleteSession.isPending}
        disabled={deleteSession.isPending}
      />

      {batchDeleteDialogOpen && (
        <BatchDeleteDialog
          open={batchDeleteDialogOpen}
          onOpenChange={setBatchDeleteDialogOpen}
          title="确认批量删除会话"
          selectedCount={selectedSessionIds.size}
          agentId={agentId}
          sessionIds={Array.from(selectedSessionIds)}
          onSuccess={handleCancelBatchDelete}
          onCancel={handleCancelBatchDeleteDialog}
          loading={multiDeleteSessions.isPending}
          disabled={multiDeleteSessions.isPending || selectedSessionIds.size === 0}
        />
      )}
    </>
  );
}
