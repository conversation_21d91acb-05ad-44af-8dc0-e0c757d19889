"use client";

import { useMultiDeleteSessions } from "@/service/session-service";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useTranslations } from "next-intl";

interface BatchDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  selectedCount: number;
  agentId: string;
  sessionIds: string[];
  onSuccess?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  disabled?: boolean;
}

export const BatchDeleteDialog = ({
  open,
  onOpenChange,
  title,
  selectedCount,
  agentId,
  sessionIds,
  onSuccess,
  onCancel,
  loading = false,
  disabled = false,
}: BatchDeleteDialogProps) => {
  const { toast } = useToast();
  const multiDeleteSessions = useMultiDeleteSessions();
  const t = useTranslations("agent.batchDelete");

  const defaultTitle = title || t("title");
  const getDescription = t("description", { count: selectedCount });

  const handleBatchDelete = async () => {
    if (selectedCount === 0) {
      toast({
        title: t("error"),
        description: t("noSelection"),
        variant: "destructive",
      });

      return;
    }

    try {
      // 如果是全选，传递空数组表示删除所有会话
      const sessionIdsToDelete = sessionIds;

      await multiDeleteSessions.mutateAsync({
        agent_id: agentId,
        session_ids: sessionIdsToDelete,
      });

      toast({
        title: t("success", { count: selectedCount }),
        description: t("success", { count: selectedCount }),
      });

      onSuccess?.();
    } catch (error) {
      console.error("批量删除会话失败:", error);
      toast({
        title: t("error"),
        description: t("failed"),
        variant: "destructive",
      });
    }
  };

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title={defaultTitle}
      description={getDescription}
      onConfirm={handleBatchDelete}
      onCancel={onCancel}
      confirmText={t("confirmText")}
      cancelText={t("cancelText")}
      loading={loading || multiDeleteSessions.isPending}
      disabled={disabled || selectedCount === 0}
    />
  );
};
