"use client";

import { useChatHistory } from "@/hooks/use-chat-history";
import { useChatMessageState, useSendMessageStream } from "@/hooks/use-chat-stream";
import { deduplicateArray, formatMsgLocalTime } from "@/lib/chat-utils";
import { Conversation } from "@/service/session-service";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { cn } from "@ragtop-web/ui/lib/utils";
import { Loader2 } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { v4 as uuid } from "uuid";
import { ChatInput } from "./chat-input";
import { ChatMessage } from "./chat-message";

interface ChatContainerProps {
  className?: string;
  agentName?: string;
  sessionId: string;
  agentId: string;
}

export function ChatContainer({
  className,
  agentName = "助手",
  sessionId,
  agentId,
}: ChatContainerProps) {
  const [tempMessage, setTempMessage] = useState<Conversation[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { sendMessage } = useSendMessageStream();
  // 使用新的聊天历史 Hook
  const {
    messages: historyMessages,
    isLoading: isLoadingHistory,
    hasMore,
    loadMore,
  } = useChatHistory({
    sessionId,
    pageSize: 20,
    enabled: !!sessionId && isInitialized,
  });

  const { message, isStreaming, startStream, cancelStream } = useChatMessageState(sendMessage);

  // 用 useMemo 缓存历史消息渲染结果
  const historyRender = useMemo(() => {
    return historyMessages.map((itemMesg, index) => (
      <ChatMessage
        index={index}
        key={itemMesg.id}
        role={itemMesg.role}
        agentName={agentName}
        message={itemMesg}
      />
    ));
  }, [historyMessages, agentName]);

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // 监听 message.content 变化并更新 tempMessage 中对应的项
  useEffect(() => {
    if (message?.id) {
      setTempMessage((prev) => {
        return deduplicateArray([...prev, message], message.id);
      });
    }
  }, [message, message?.id]);

  // 自动滚动到底部当有新消息时
  useEffect(() => {
    if (scrollAreaRef.current && isAutoScrolling) {
      requestAnimationFrame(() => {
        const scrollContainer = scrollAreaRef.current;

        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      });
    }
  }, [historyMessages, tempMessage, isAutoScrolling]);

  // 处理滚动事件，实现向上滚动加载更多
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      // 检测是否滚动到顶部，加载更多历史消息
      if (scrollTop === 0 && hasMore && !isLoadingHistory) {
        loadMore();
      }

      // 检测是否接近底部，启用自动滚动
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

      setIsAutoScrolling(isNearBottom);
    },
    [hasMore, isLoadingHistory, loadMore]
  );

  const handleSendMessage = (content: string) => {
    // 检查必要参数
    if (!sessionId || !agentId) {
      toast({
        title: "错误",
        description: "缺少会话或代理信息",
        variant: "destructive",
      });

      return;
    }

    // Add user message
    const userMessage: Conversation = {
      id: `template-question` + `-${  uuid()}`,
      role: "user",
      createTime: formatMsgLocalTime(Date.now()),
      content,
    };

    setTempMessage((prev) => [...prev, userMessage]);

    // 启用自动滚动（发送新消息时）
    setIsAutoScrolling(true);

    // 发送流式消息
    startStream({
      agent_id: agentId,
      session_id: sessionId,
      text_content: content,
    });
  };

  return (
    <div className={cn("flex h-full flex-col", className)}>
      <div
        className="no-scrollbar flex-1 overflow-y-auto"
        ref={scrollAreaRef}
        onScroll={handleScroll}
      >
        {historyMessages.length === 0 && !isLoadingHistory ? (
          <div className="text-muted-foreground flex h-full items-center justify-center">
            开始新的对话...
          </div>
        ) : (
          <div>
            {/* 顶部加载更多指示器 */}
            {hasMore && (
              <div className="text-muted-foreground flex items-center justify-center p-4">
                {isLoadingHistory ? (
                  <>
                    <div className="text-center">
                      <Loader2 className="mx-auto mb-4 h-8 w-8 animate-spin text-gray-900 dark:text-gray-200" />
                      <p className="text-gray-600 dark:text-gray-400">加载历史信息中...</p>
                    </div>
                  </>
                ) : (
                  <span className="text-sm">向上滚动加载更多历史消息</span>
                )}
              </div>
            )}

            {/* 消息列表 */}
            {/* 历史消息（渲染后缓存，永不再 render） */}
            {historyRender}
            {/* 新消息（只渲染新消息部分） */}
            {tempMessage.map((itemMesg, index) => (
              <ChatMessage
                index={historyMessages.length + index}
                key={itemMesg.id}
                role={itemMesg.role}
                agentName={agentName}
                message={itemMesg}
              />
            ))}

            {/* 流式回复指示器 */}
            {/* {isStreaming && (
              <div className="flex items-center space-x-2 text-muted-foreground -mt-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>正在生成回复...</span>
              </div>
            )} */}
          </div>
        )}
      </div>

      <div className="mt-auto pt-4">
        <ChatInput
          placeholder={"请输入您的问题..."}
          onSend={handleSendMessage}
          onCancel={cancelStream}
          isStreaming={isStreaming}
          disabled={isStreaming || isLoadingHistory}
        />
      </div>
    </div>
  );
}
