"use client";

import { useConfig } from "@/components/config-provider-client";
import {
  buildColumns,
  buildData,
  downloadFile,
  fetchCSV,
  getFileTypeFromName,
} from "@/lib/chat-utils";
import { usePreviewFile } from "@/service/file-service";
import { Button } from "@ragtop-web/ui/components/button";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { ColumnDef, DataTable } from "@ragtop-web/ui/components/data-table";
import { cn } from "@ragtop-web/ui/lib/utils";
import { Download, Eye, FileText } from "lucide-react";
import { useEffect, useState } from "react";
import { PdfDrawer } from "./pdf-drawer";

/**
 * 文件内容接口
 */
export interface FileDisplayItem {
  title: string;
  type: string;
  uri: string;
  viewMode?: string[];
  content?: string;
}

interface FileDisplayProps {
  files: FileDisplayItem[];
  className?: string;
}

/**
 * 获取文件图标
 */
const getFileIcon = (fileType: string) => {
  return <FileText className={cn("h-4 w-4 text-gray-500 dark:text-gray-400")} />;
};

/**
 * 处理文件下载
 */
const handleFileDownload = (file: FileDisplayItem) => {
  downloadFile(file.uri, file.title);
};

interface FilePreviewProps {
  uri: string;
  maxRows?: number;
  maxColumns?: number;
  csvPreviewMode?: "sample" | "all";
}

const FilePreview = ({
  uri,
  maxRows,
  maxColumns = 20,
  csvPreviewMode = "all",
}: FilePreviewProps) => {
  const [tableData, setTableData] = useState<{ [k: string]: string | undefined }[] | undefined>(
    undefined
  );
  const [columns, setColumns] = useState<ColumnDef<Record<string, any>, any>[] | undefined>(
    undefined
  );
  const [totalRows, setTotalRows] = useState<number>(0);
  const previewFile = usePreviewFile();

  useEffect(() => {
    if (uri && csvPreviewMode === "sample") {
      previewFile.mutate(
        { url: uri, max_rows: maxRows + 1, max_columns: maxColumns },
        {
          onSuccess: (fileData: any) => {
            const { total_rows, rows: dataRows, total_columns } = fileData;

            if (dataRows && dataRows.length > 0) {
              const header = dataRows[0];

              setColumns(buildColumns(header));
              setTableData(buildData(dataRows));
              setTotalRows(total_rows - 1 || dataRows.length);
            }
          },
        }
      );
    } else {
      fetchCSV(uri).then((res) => {
        if (res && res.length > 0) {
          if (res[0]) {
            const columns = buildColumns(res[0]) || [];

            setColumns(columns);
          }
          const data = buildData(res) || [];

          setTableData(data);
          setTotalRows(data.length);
        }
      });
    }
  }, [uri]);

  if (columns && tableData) {
    return (
      <div className="max-h-[85vh]">
        <div className="mb-2 flex items-center text-xs text-gray-500">
          {totalRows > tableData.length && tableData.length != 0 ? (
            <div>
              <span>总行数: {tableData.length} +</span>
              <span className="ml-4 text-xs text-gray-400">（仅展示前 {tableData.length} 行）</span>
            </div>
          ) : (
            <span>总行数: {tableData.length} </span>
          )}
        </div>
        <DataTable columns={columns} data={tableData} className="max-h-[80vh] overflow-auto" />
      </div>
    );
  }
};

/**
 * 文件展示组件
 *
 * 用于在聊天消息中展示文件列表，支持文件下载
 */
export function FileDisplay({ files, className }: FileDisplayProps) {
  const [pdfPreviewOpen, setPdfPreviewOpen] = useState(false);
  const [currentPdfUrl, setCurrentPdfUrl] = useState("");
  const [currentPdfName, setCurrentPdfName] = useState("");

  // CSV 预览相关状态
  const [csvPreviewOpen, setCsvPreviewOpen] = useState(false);
  const [currentCsvUrl, setCurrentCsvUrl] = useState("");
  const [currentCsvName, setCurrentCsvName] = useState("");
  const [csvPreviewMode, setCsvPreviewMode] = useState<"sample" | "all">("all");

  const {FILE_PREVIEW_MAX_ROWS} = useConfig();

  if (!files || files.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="space-y-2">
        {files.map((file, index) => {
          const fileType = getFileTypeFromName(file.type.split("/").pop() || "");

          return (
            <div key={index}>
              <div className="mt-2 flex items-center justify-between rounded-sm border border-gray-200 bg-gray-100 px-2 py-1 text-gray-800 transition-colors hover:bg-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700">
                <div className="flex min-w-0 flex-1 items-center space-x-2">
                  {getFileIcon(fileType)}
                  <div className="flex min-w-0 flex-1 flex-row">
                    <span className="block max-w-[450px] truncate text-sm font-medium">
                      {/* 有些文件名包含.，需要去掉最后一个. */}
                      {file.title.split(".").slice(0, -1).join(".")}
                    </span>
                    <span className="text-sm font-medium">{`.${  fileType.toLowerCase()}`}</span>
                  </div>
                </div>
                <div className="ml-2 flex items-center space-x-1">
                  {/* 仅 PDF 文件显示 Eye 图标 */}
                  {fileType === "PDF" && (
                    <Button
                      onClick={() => {
                        // setCurrentChunk(chunk);
                        setPdfPreviewOpen(true);
                        setCurrentPdfUrl(file.uri);
                        setCurrentPdfName(file.title);
                      }}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                    >
                      <Eye className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    </Button>
                  )}
                  {/* CSV 文件显示 Eye 图标，点击弹出抽屉 */}
                  {fileType === "CSV" && (
                    <Button
                      onClick={() => {
                        setCsvPreviewOpen(true);
                        setCurrentCsvUrl(file.uri);
                        setCurrentCsvName(file.title);
                        if (file.viewMode?.includes("show_directly")) {
                          setCsvPreviewMode("sample");
                        } else {
                          setCsvPreviewMode("all");
                        }
                      }}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                    >
                      <Eye className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    </Button>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleFileDownload(file)}
                    title="下载文件"
                  >
                    <Download className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </Button>
                </div>
              </div>
              {file.viewMode?.includes("show_directly") && (
                <div className="mt-2">
                  <FilePreview
                    uri={file.uri}
                    csvPreviewMode="sample"
                    maxRows={FILE_PREVIEW_MAX_ROWS}
                    maxColumns={20}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
      {/* PdfDrawer 组件 */}
      {pdfPreviewOpen && (
        <PdfDrawer
          url={currentPdfUrl}
          fileName={currentPdfName}
          open={pdfPreviewOpen}
          onOpenChange={setPdfPreviewOpen}
          // chunk={{}}
        />
      )}

      {/* CSV 预览抽屉 */}
      <CustomDrawer
        open={csvPreviewOpen}
        onClose={() => setCsvPreviewOpen(false)}
        title={currentCsvName || "CSV 预览"}
        footer={null}
        className="sm:max-w-2xl"
      >
        <div>
          {csvPreviewOpen && (
            <FilePreview
              uri={currentCsvUrl}
              maxRows={FILE_PREVIEW_MAX_ROWS}
              maxColumns={20}
              csvPreviewMode={csvPreviewMode}
            />
          )}
        </div>
      </CustomDrawer>
    </div>
  );
}
