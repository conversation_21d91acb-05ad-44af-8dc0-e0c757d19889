"use client";

import DOMPurify from "dompurify";
import { ExternalLink, FileText } from "lucide-react";
import { useCallback, useContext, useState } from "react";

import { AnnotationsContext } from "@/components/markdown-renderer";
import { replaceFilesOriginPlaceholder } from "@/lib/chat-utils";
import { Button } from "@ragtop-web/ui/components/button";
import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover";
import { ImagePreview } from "../image-preview";
import { PdfDrawer } from "../pdf-drawer";

interface QuoteProps {
  index: number;
}

const Quote = ({ index }: QuoteProps) => {
  const annotations = useContext(AnnotationsContext);
  const [pdfPreviewOpen, setPdfPreviewOpen] = useState(false);
  const [currentChunk, setCurrentChunk] = useState<any>(null);
  const [currentPdfUrl, setCurrentPdfUrl] = useState("");
  const [currentPdfName, setCurrentPdfName] = useState("");

  const getReferenceInfo = useCallback(
    (refIndex: number) => {
      const annotationContentById = annotations.find((item) => item.annotation_id == refIndex);

      if (!annotationContentById) {
        return null;
      } else {
        const { doc_uri, document_name, image_uri, content, positions } =
          annotationContentById?.data?.value;
        const docUrl = replaceFilesOriginPlaceholder(doc_uri);
        const imageUri = replaceFilesOriginPlaceholder(image_uri);

        return {
          doc_uri: docUrl,
          document_name,
          image_uri: imageUri,
          content,
          positions,
        };
      }
    },
    [annotations]
  );

  /**
   * 判断是否为 PDF 文件
   */
  const isPdfFile = useCallback((fileName: string) => {
    return fileName?.toLowerCase().endsWith(".pdf");
  }, []);

  /**
   * 处理文档点击事件
   */
  const handleDocumentClick = useCallback(
    (referenceInfo: any) => {
      const fileName = referenceInfo.document_name || "";
      const url = referenceInfo.doc_uri;

      if (!url) {
        console.warn("文档没有可用的链接");

        return;
      }

      if (isPdfFile(fileName)) {
        // PDF 文件处理 - 打开抽屉预览
        setCurrentPdfUrl(url);
        setCurrentPdfName(fileName);
        setCurrentChunk(referenceInfo);
        setPdfPreviewOpen(true);
      } else {
        // 其他文件类型，在新标签页中打开
        window.open(url, "_blank", "noopener,noreferrer");
      }
    },
    [isPdfFile]
  );

  /**
   * 获取 Popover 内容
   */
  const getPopoverContent = useCallback(
    (refIndex: number) => {
      const referenceInfo = getReferenceInfo(refIndex);

      if (!referenceInfo) {
        return <div className="text-muted-foreground p-2 text-sm">引用信息暂未返回</div>;
      }
      const imageUri = referenceInfo.image_uri;
      const {content} = referenceInfo;
      const documentName = referenceInfo.document_name;
      const hasUrl = !!referenceInfo.doc_uri;

      return (
        <div className="max-h-[400px] w-[800px] max-w-[800px] overflow-y-auto p-4">
          <div className="mb-3 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="text-sm font-medium">引用内容</span>
          </div>

          <div className="flex gap-4">
            {/* 左侧图片 */}
            {imageUri && (
              <div className="h-32 w-32 flex-shrink-0">
                <ImagePreview
                  imageUri={imageUri}
                  alt="引用图片"
                  className="h-full w-full"
                  width={128}
                  height={128}
                  showZoomIcon={true}
                  fallbackIcon={
                    <div className="bg-muted text-muted-foreground flex h-full w-full items-center justify-center rounded border text-xs">
                      图片
                    </div>
                  }
                />
              </div>
            )}

            {/* 右侧文字内容 */}
            <div className="min-w-0 flex-1">
              {/* 引用文本内容 */}
              {content && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(content),
                  }}
                ></div>
              )}

              {/* 文档名称 */}
              {documentName && (
                <div className="text-muted-foreground text-xs">
                  {hasUrl ? (
                    <button
                      onClick={() => handleDocumentClick(referenceInfo)}
                      className="hover:text-primary flex cursor-pointer items-center gap-1 hover:underline"
                    >
                      <span>{documentName}</span>
                      <ExternalLink className="h-3 w-3" />
                    </button>
                  ) : (
                    <span>{documentName}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      );
    },
    [getReferenceInfo, handleDocumentClick]
  );

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="ml-1 h-5 w-5 rounded-full border-0 bg-gray-200 text-xs font-medium text-gray-700 shadow-none transition-all duration-200 hover:bg-gray-300"
          >
            {Number(index) + 1}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto max-w-none p-0">
          {getPopoverContent(index)}
        </PopoverContent>
      </Popover>

      {/* PDF 预览组件 */}
      {pdfPreviewOpen && (
        <PdfDrawer
          url={currentPdfUrl}
          fileName={currentPdfName}
          open={pdfPreviewOpen}
          onOpenChange={setPdfPreviewOpen}
          chunk={currentChunk}
        />
      )}
    </>
  );
};

export default Quote;
