"use client";

import { useTheme } from "next-themes";
import ReactMarkdown from "react-markdown";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { prism, vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import remarkGfm from "remark-gfm";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import VegaLiteChart from "@ragtop-web/ui/components/vega-chart/lite-vega-chart";
import { cn } from "@ragtop-web/ui/lib/utils";
import { flow } from "lodash-es";
import { createContext, memo, useMemo, useRef, useState } from "react";
import rehypeKatex from "rehype-katex";
import rehypeRaw from "rehype-raw";
import remarkDirective from "remark-directive";
import remarkMath from "remark-math";
import remarkPlugin from "../lib/remark-plugin";
import remarkReferencePlugin from "../lib/remark-reference-plugin";
import remarkMarkdown from "./remark";
// 注意：如果样式导入有问题，可以注释掉下面这行
// import 'highlight.js/styles/github.css'
import { Check, Copy } from "lucide-react";
import { marked } from "marked";
import { copyToClipboard } from "../lib/utils";

const IMAGE_PLACEHOLDER = `RAGTOP_FILE_SERVICE_PLACEHOLDER_86aef5533ef0dd`;

interface MarkdownRendererProps {
  id: string;
  content: string;
  annotations?: any[];
  files?: any[];
  allowDangerousHtml?: boolean;
}

function escapeDollarNumber(text: string): string {
  let isInBlockCode = false;

  return text
    .split("\n")
    .map((line) => {
      // 检查 ``` 数量，优先处理 fenced code block
      const tripleCount = (line.match(/```/g) || []).length;

      if (tripleCount > 0) {
        if (tripleCount % 2 === 1) {
          isInBlockCode = !isInBlockCode;
        }

        return line;
      }

      if (!isInBlockCode) {
        // 用正则拆分 line，保留 inline code 段
        return line
          .split(/(`+[^`]*`+)/g)
          .map((segment, idx) => {
            // 仅对非 inline code（偶数段）做转义
            if (idx % 2 === 0) {
              return segment.replace(/(\$)/g, (match, p1, offset, str) => {
                const prev = str[offset - 1];

                if (prev === "`" || prev === "\\") {
                  return match;
                }

                return `\\${match}`;
              });
            }

            return segment;
          })
          .join("");
      }

      return line;
    })
    .join("\n");
}

export const preprocessLaTeX = (content: string) => {
  // Escape $ to \$ to prevent conflicts with inline LaTeX
  const contentFmt = escapeDollarNumber(content);

  // Replace block-level LaTeX delimiters \[ \] with $$ $$
  const blockProcessedContent = contentFmt.replace(
    /\\\[(.*?)\\\]/gs,
    (_, equation) => `$$${equation}$$`
  );
  // Replace inline LaTeX delimiters \( \) with $ $
  const inlineProcessedContent = blockProcessedContent.replace(
    /\\\((.*?)\\\)/gs,
    (_, equation) => `$${equation}$`
  );

  return inlineProcessedContent;
};

// 处理leafDirective 和 containerDirective 的格式
// 忽略代码块中的内容
function preprocessMarkdown(markdown: string): string {
  const codeBlocks: string[] = [];
  const inlineCodeBlocks: string[] = [];

  let processed = markdown;

  // 提取所有代码块
  processed = processed.replace(/```[\s\S]*?```/g, (match) => {
    const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`;

    codeBlocks.push(match);

    return placeholder;
  });

  processed = processed.replace(/`[^`\n]*`/g, (match) => {
    const placeholder = `__INLINE_CODE_${inlineCodeBlocks.length}__`;

    inlineCodeBlocks.push(match);

    return placeholder;
  });

  // 处理指令
  processed = processed
    .replace(/::+quote\[(.+?)\]/g, ":quote[$1]")
    .replace(/(\]\s*):([a-zA-Z]+\[)/g, "] :$2");

  // 恢复代码块
  codeBlocks.forEach((block, index) => {
    processed = processed.replace(`__CODE_BLOCK_${index}__`, block);
  });

  inlineCodeBlocks.forEach((block, index) => {
    processed = processed.replace(`__INLINE_CODE_${index}__`, block);
  });

  return processed;
}

// --- Block splitting and memoized rendering ---
const lexer = (() => {
  let lastText = "";
  let lastResult: string[] = [];

  return (markdown: string): string[] => {
    if (markdown === lastText) {
      return lastResult;
    }
    lastText = markdown;
    const tokens = marked.lexer(markdown);

    lastResult = tokens.map((token) => token.raw);

    return lastResult;
  };
})();

const MarkdownBlock = memo(
  function PureMarkdownBlock({
    id,
    index,
    blockIndex,
    content,
  }: {
    id: string;
    index: number;
    blockIndex: number;
    content: string;
  }) {
    if (content === undefined) {
      return null;
    }

    // 只渲染单个 block，依然用 ReactMarkdown 以保持原有渲染能力
    return (
      <ReactMarkdown
        remarkPlugins={[
          remarkGfm,
          remarkMath,
          remarkReferencePlugin,
          remarkDirective,
          remarkPlugin,
        ]}
        rehypePlugins={[rehypeRaw, rehypeKatex]}
        components={MarkdownRendererComponents}
      >
        {content}
      </ReactMarkdown>
    );
  },
  (prevProps, nextProps) =>
    prevProps.id === nextProps.id &&
    prevProps.index === nextProps.index &&
    prevProps.blockIndex === nextProps.blockIndex &&
    prevProps.content === nextProps.content
);

const TextPart = memo(
  function PureTextPart({ id, index, content }: { id: string; index: number; content: string }) {
    const blocks = lexer(content);

    return (
      <>
        {blocks.map((block, i) => (
          <MarkdownBlock
            key={`${id}-${index}-${i}`}
            id={id}
            index={index}
            blockIndex={i}
            content={block}
          />
        ))}
      </>
    );
  },
  (prev, next) => prev.content === next.content
);

function getVegaChartTitle(title: string | object | undefined): string {
  if (!title) {
    return "";
  }

  if (typeof title === "string") {
    return title;
  }

  if (typeof title === "object" && title !== null) {
    // If title is an object, try to extract the text property
    if ("text" in title && typeof title.text === "string") {
      return title.text;
    }

    // Fallback: try to stringify the object or use a default
    return "图表";
  }

  return "";
}

// --- Extracted components for reuse ---
const MarkdownRendererComponents = {
  // 自定义代码块和内联代码样式
  code: ({ node, className, children, ...props }: any) => {
    const { resolvedTheme = "dark" } = useTheme();
    const [copied, setCopied] = useState(false);
    const match = /language-(\w+)/.exec(className || "");
    const codeStr = String(children).replace(/\n$/, "");

    if (match) {
      if (match[1] === "vega") {
        const specs = JSON.parse(codeStr);

        const [selected, setSelected] = useState(0);

        return (
          <div>
            <div className="flex w-full">
              <Select value={String(selected)} onValueChange={(v) => setSelected(Number(v))}>
                <SelectTrigger className="mb-2 h-6 w-max">
                  <SelectValue className="text-white">
                    {getVegaChartTitle(specs[selected]?.title) || `图表${selected + 1}`}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {specs.map((spec: any, idx: any) => (
                    <SelectItem key={idx} value={String(idx)}>
                      {getVegaChartTitle(spec.title) || `图表${idx + 1}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="relative mx-auto aspect-[1.6/1] w-full rounded-lg shadow">
              <div className="h-full w-full">
                {/* todo 配置放在 VegaLiteChart */}
                <VegaLiteChart spec={specs[selected]} className="h-full w-full" />
              </div>
            </div>
          </div>
        );
      } else {
        return (
          <div className="group relative">
            <button
              className="absolute top-2 right-2 z-10 flex items-center gap-1 rounded bg-zinc-800/80 px-2 py-1 text-sm text-white transition-opacity"
              onClick={async () => {
                const success = await copyToClipboard(codeStr);

                setCopied(success);
                setTimeout(() => setCopied(false), 1200);
              }}
              type="button"
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              <span>{copied ? "已复制" : "复制"}</span>
            </button>
            <SyntaxHighlighter
              {...props}
              PreTag="div"
              language={match[1]}
              style={resolvedTheme === "dark" ? vscDarkPlus : prism}
            >
              {codeStr}
            </SyntaxHighlighter>
          </div>
        );
      }
    } else {
      return (
        <code
          {...props}
          className={cn(
            "bg-muted relative rounded-md px-[0.3rem] py-[0.2rem] font-mono text-sm font-medium",
            "border-border/50 border",
            "text-foreground",
            "before:content-[''] after:content-['']",
            "transition-colors duration-200",
            "hover:bg-muted/80",
            className
          )}
        >
          {children}
        </code>
      );
    }
  },
  // 自定义表格样式
  table: ({ children }: any) => (
    <div className="overflow-x-auto">
      <table className="border-border min-w-full border-collapse border">{children}</table>
    </div>
  ),
  th: ({ children }: any) => (
    <th className="border-border bg-muted border px-4 py-2 text-left font-medium">{children}</th>
  ),
  td: ({ children }: any) => <td className="border-border border px-4 py-2">{children}</td>,
  // 自定义链接样式
  a: ({ children, href }: any) => (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="text-primary hover:underline"
    >
      {children}
    </a>
  ),
  // 自定义引用块样式
  blockquote: ({ children }: any) => (
    <blockquote className="border-primary text-muted-foreground border-l-4 pl-4 italic">
      {children}
    </blockquote>
  ),
  // 自定义列表样式
  ul: ({ children }: any) => <ul className="list-inside list-disc space-y-1">{children}</ul>,
  ol: ({ children }: any) => <ol className="list-inside list-decimal space-y-1">{children}</ol>,
  li: ({ children }: any) => <li>{children}</li>,
  // 自定义标题样式
  h1: ({ children }: any) => (
    <h1 className="mt-6 mb-4 text-2xl font-bold first:mt-0">{children}</h1>
  ),
  h2: ({ children }: any) => (
    <h2 className="mt-5 mb-3 text-xl font-semibold first:mt-0">{children}</h2>
  ),
  h3: ({ children }: any) => (
    <h3 className="mt-4 mb-2 text-lg font-medium first:mt-0">{children}</h3>
  ),
  h4: ({ children }: any) => (
    <h4 className="mt-3 mb-2 text-base font-medium first:mt-0">{children}</h4>
  ),
  h5: ({ children }: any) => (
    <h5 className="mt-2 mb-1 text-sm font-medium first:mt-0">{children}</h5>
  ),
  h6: ({ children }: any) => (
    <h6 className="mt-2 mb-1 text-xs font-medium first:mt-0">{children}</h6>
  ),
  // 自定义强调文本样式
  strong: ({ children }: any) => <strong>{children}</strong>,
  em: ({ children }: any) => <em>{children}</em>,
  // 自定义段落样式，处理引用
  p: ({ children }: any) => <p className="leading-relaxed">{children}</p>,
  // 自定义 quote directive 渲染
  quote: ({ attributes, children }: any) => {
    const index = children || 0;
    const QuoteComponent = remarkMarkdown.quote;

    return <QuoteComponent index={index} />;
  },
};

// 创建 context
// 用于传递 annotations 到 quote 组件
// 不要把 annotations 作为 props 逐层传递，用 context 或 ref 缓存到顶层，引用时再用 useContext/useRef 获取。
export const AnnotationsContext = createContext<any[]>([]);

// --- Main MarkdownRenderer ---
export function MarkdownRenderer({ content, annotations = [], id }: MarkdownRendererProps) {
  const annotationsRef = useRef(annotations);

  annotationsRef.current = annotations;
  const contentWithCursor = useMemo(() => {
    const calculate = flow(preprocessLaTeX, preprocessMarkdown);

    return calculate(content);
  }, [content]);

  const contentWithFiles = contentWithCursor.replace(
    new RegExp(IMAGE_PLACEHOLDER, "g"),
    window?.location?.origin
  );

  // 用 TextPart 进行分块渲染
  return (
    <AnnotationsContext.Provider value={annotationsRef.current}>
      <div className={cn("prose prose-sm dark:prose-invert max-w-none text-sm")}>
        <TextPart id={id} index={0} content={contentWithFiles} />
      </div>
    </AnnotationsContext.Provider>
  );
}
