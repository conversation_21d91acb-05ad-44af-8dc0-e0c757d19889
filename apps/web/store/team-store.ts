/**
 * 团队状态管理
 *
 * 使用Jotai管理团队相关的状态，结合localStorage持久化存储
 */

import { Team, TeamType, User } from "@/service/team-service";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// 定义登录响应中的团队类型
export interface LoginTeam {
  id: string;
  title: string;
  roles?: string[];
  member_id: string;
  type: TeamType;
}

/**
 * 当前团队信息的atom
 * 使用localStorage持久化存储
 */
export const currentTeamAtom = atomWithStorage<Team | LoginTeam | null>(
  "current_team",
  null,
  undefined,
  {
    getOnInit: true,
  }
);

/**
 * 当前用户信息的atom
 * 使用localStorage持久化存储
 */
export const currentUserAtom = atomWithStorage<User | null>("current_user", null, undefined, {
  getOnInit: true,
});

/**
 * 当前用户是否为团队管理员的atom
 * 基于当前团队信息计算得出
 */
export const isTeamAdminAtom = atom<boolean>((get) => {
  const currentTeam = get(currentTeamAtom);

  return currentTeam?.roles?.includes("TEAM_ADMIN") || false;
});

/**
 * 获取团队ID的工具函数
 * 优先从Jotai获取，降级到localStorage
 */
export const getTeamId = (): string | null => {
  if (typeof window === "undefined") {return null;}

  try {
    // 尝试从localStorage获取（因为使用了atomWithStorage，Jotai和localStorage是同步的）
    const currentTeam = getCurrentTeam();

    if (currentTeam) {
      return currentTeam?.id;
    }

    return null;
  } catch (error) {
    console.error("获取团队ID时出错:", error);

    return null;
  }
};

/**
 * 获取当前团队信息的工具函数
 * 优先从Jotai获取，降级到localStorage
 */
export const getCurrentTeam = (): Team | LoginTeam | null => {
  if (typeof window === "undefined") {return null;}
  try {
    const teamStr = localStorage.getItem("current_team");

    if (!teamStr) {return null;}

    return JSON.parse(teamStr);
  } catch (error) {
    console.error("获取当前团队信息时出错:", error);

    return null;
  }
};

/**
 * 设置当前团队信息的写入atom
 * 同时更新团队ID和团队信息
 */
export const setCurrentTeamAtom = atom(null, (_, set, team: Team | LoginTeam | null) => {
  // 更新团队信息
  set(currentTeamAtom, team);
});

/**
 * 团队列表的atom
 */
export const teamListAtom = atom<Team[]>([]);

/**
 * 从用户团队列表初始化当前团队的写入atom
 */
export const initTeamFromUserAtom = atom(null, (get, set, teams: (Team | LoginTeam)[]) => {
  const teamId = getTeamId();

  if (teams && teams.length > 0) {
    // 查找当前团队
    // 避免水合报错，直接使用"PERSONAL"，而不是TeamType.Personal
    const currentTeam = teamId
      ? teams.find((team) => team.id === teamId)
      : teams.find(({ type }) => type === "PERSONAL");

    if (currentTeam) {
      // 设置当前团队信息
      set(setCurrentTeamAtom, currentTeam || teams[0]);
    } else if (teams[0]) {
      // 如果没有找到当前团队，使用第一个团队
      set(setCurrentTeamAtom, teams[0]);
    }
  } else {
    // 如果没有团队，清空当前团队信息
    set(setCurrentTeamAtom, null);
  }
});

/**
 * 切换团队的写入atom
 * 切换后会刷新页面重新获取数据
 */
export const switchTeamAtom = atom(null, (_, set, team: Team | LoginTeam) => {
  // 设置新的团队信息
  set(setCurrentTeamAtom, team);

  // 刷新页面以重新获取数据
  if (typeof window !== "undefined") {
    window.location.reload();
  }
});

/**
 * 清理所有认证相关状态的写入atom
 * 用于退出登录或认证失效时
 */
export const clearAuthStateAtom = atom(null, (_, set) => {
  // 清理用户信息
  set(currentUserAtom, null);
  // 清理团队信息
  set(currentTeamAtom, null);

  // 清理团队列表
  set(teamListAtom, []);
});
