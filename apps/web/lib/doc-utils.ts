import { IReferenceChunk } from "@/service/session-service";
import { get } from "lodash-es";
import { v4 as uuid } from "uuid";

export const buildChunkHighlights = (
  selectedChunk: IReferenceChunk,
  size: { width: number; height: number }
) => {
  return Array.isArray(selectedChunk?.positions) &&
    selectedChunk.positions.every((x) => Array.isArray(x))
    ? selectedChunk?.positions?.map((x) => {
        const boundingRect = {
          width: size.width,
          height: size.height,
          x1: x[1],
          x2: x[2],
          y1: x[3],
          y2: x[4],
        };

        return {
          id: uuid(),
          comment: {
            text: "",
            emoji: "",
          },
          content: {
            text: get(selectedChunk, "content_with_weight") || get(selectedChunk, "content", ""),
          },
          position: {
            boundingRect,
            rects: [boundingRect],
            pageNumber: x[0],
          },
        };
      })
    : [];
};
