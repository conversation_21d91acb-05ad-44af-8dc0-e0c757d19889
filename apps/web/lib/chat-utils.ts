import { Message, MessageStep } from "@/hooks/use-chat-stream";
import { ChatMessage, Content, ContentType, Conversation } from "@/service/session-service";
import { createColumnHelper } from "@ragtop-web/ui/components/data-table";
import { v4 as uuid } from "uuid";

export const IMAGE_PLACEHOLDER = `RAGTOP_FILE_SERVICE_PLACEHOLDER_86aef5533ef0dd`;

export const replaceFilesOriginPlaceholder = (content: string) => {
  if (!content) {return "";}

  return content.replace(new RegExp(IMAGE_PLACEHOLDER, "g"), window?.location?.origin);
};

export function formatMsgLocalTime(ts?: string | number | Date): string {
  if (!ts) {return "";}
  const date = new Date(ts);
  const now = new Date();

  const isToday =
    date.getFullYear() === now.getFullYear() &&
    date.getMonth() === now.getMonth() &&
    date.getDate() === now.getDate();

  if (isToday) {
    // 只显示几点几分
    return date.toLocaleTimeString(undefined, {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  }

  const isThisYear = date.getFullYear() === now.getFullYear();

  if (isThisYear) {
    // 显示 "06月21日 10:53"
    return date
      .toLocaleString(undefined, {
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      })
      .replace("/", "月")
      .replace("/", "日 ");
  }

  // 显示 "2023年06月21日 10:53"
  return (
    `${date.getFullYear() 
    }年${ 
    (date.getMonth() + 1).toString().padStart(2, "0") 
    }月${ 
    date.getDate().toString().padStart(2, "0") 
    }日 ${ 
    date.toLocaleTimeString(undefined, {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    })}`
  );
}

/**
 * 将历史对话数据转换为聊天消息格式
 * @param history 历史对话数组
 * @returns 格式化后的消息数组
 */
export const convertHistoryToMessages = (history: Conversation[]): any[] => {
  const result: any[] = [];

  const reversed = [...history].reverse();

  reversed.forEach((item, index) => {
    const question = item?.question;
    const answers = item?.answers || [];

    // 处理 question
    if (question) {
      if (question.type == "USER") {
        try {
          // 处理简单的消息，例如用户的提问
          if (!question.content) {return;}
          const contentFmt = JSON.parse(question.content);
          const texts = contentFmt.filter((item: any) => item.type === "TEXT_MESSAGE_CONTENT");
          const textContent = texts?.map((item: any) => item.content);

          result.push({
            id: question.id,
            role: "user",
            content: textContent?.join(",") || "",
            isComplete: true,
            createTime: formatMsgLocalTime(question.create_time),
          });
        } catch (error) {
          console.error(`question error`, question);
        }
      }

      if (question.type == "AGENT") {
        if (!question.content) {return;}
        const foramtData: any = parseContentToStep(JSON.parse(question.content));

        result.push({
          id: question.id,
          role: "assistant",
          steps: foramtData.steps,
          isComplete: true,
          error: foramtData.error,
          isStepMsg: foramtData.isStepMsg,
          createTime: formatMsgLocalTime(question.create_time),
        });
      }
    }

    // 处理 answers 数组
    answers.forEach((answer) => {
      if (!answer.content) {
        result.push({
          id: answer.id,
          role: answer.type === "USER" ? "user" : "assistant",
          content: "未知答案",
        });
      } else {
        const foramtData: any = parseContentToStep(JSON.parse(answer.content));

        result.push({
          id: answer.id,
          role: answer.type === "USER" ? "user" : "assistant",
          steps: foramtData.steps,
          thinking: foramtData.thinking,
          isComplete: true,
          error: foramtData.error,
          isStepMsg: foramtData.isStepMsg,
          msgAutoOpen: index >= reversed.length - 10, // 最后10个为 true，其余为 false
          createTime: formatMsgLocalTime(answer.create_time),
        });
      }
    });
  });

  return result;
};

// 如果不传parentStep， 则直接挂在draft.steps， 否则挂在parentStep.subSteps
function getOrCreateStep(
  draft: Message,
  step_id?: string,
  name?: string,
  parentStep?: MessageStep
): MessageStep {
  let step: MessageStep;

  if (step_id) {
    if (parentStep) {
      if (!parentStep.subSteps) {parentStep.subSteps = [];}
      const found = parentStep.subSteps.find((s: any) => s.id === step_id);

      if (found) {
        step = found;
      } else {
        step = {
          id: step_id,
          name: name || "未命名步骤",
          status: "finished",
          messages: [],
          files: [],
          subSteps: [],
        };
        parentStep.subSteps.push(step);
      }

      return step;
    } else {
      const found = draft.steps.find((s: any) => s.id === step_id);

      if (found) {
        step = found;
      } else {
        step = {
          id: step_id,
          name: name || "未命名步骤",
          status: "finished",
          messages: [],
          files: [],
          subSteps: [],
        };
        draft.steps.push(step);
      }

      return step;
    }
  } else {
    if (draft.steps.length === 0) {
      step = {
        id: `default` + `-${  uuid()}`,
        name: "完整回答",
        status: "finished",
        messages: [],
        files: [],
        subSteps: [],
      };
      draft.steps.push(step);
    } else {
      step = draft.steps[0] ?? {
        id: `default` + `-${  uuid()}`,
        name: "完整回答",
        status: "finished",
        messages: [],
        files: [],
        subSteps: [],
      };
      if (draft.steps.length === 0) {draft.steps.push(step);}
    }

    return step;
  }
}

function findStepById(steps: MessageStep[], step_id: string): MessageStep | undefined {
  for (const step of steps) {
    if (step.id === step_id) {return step;}
    if (step.subSteps && step.subSteps.length > 0) {
      const found = findStepById(step.subSteps, step_id);

      if (found) {return found;}
    }
  }

  return undefined;
}

function resolveStep(draft: Message, item: any): MessageStep {
  if (item.parent_step_id) {
    // 递归查找 parent_step_id 是否在多级 subSteps 中
    const parentStep = findStepById(draft.steps, item.parent_step_id);

    if (parentStep) {
      // 父 step 已存在，放到 subSteps
      return getOrCreateStep(draft, item.step_id, item.name, parentStep);
    } else {
      // 父 step 不存在，直接作为顶层 step
      return getOrCreateStep(draft, item.step_id, item.name);
    }
  } else if (item.step_id) {
    // 兼容：step_id 可能是 subStep，但没有 parent_step_id
    let step = draft.steps.find((s: any) => s.id === item.step_id);

    if (!step) {
      step = findStepById(draft.steps, item.step_id);
    }
    if (step) {return step;}

    return getOrCreateStep(draft, item.step_id, item.name);
  } else {
    return getOrCreateStep(draft);
  }
}

function resolveMessage(step: MessageStep, message_id: string, annotation_matcher?: any[]): any {
  let msg = step.messages.find((m: any) => m.id === message_id);

  if (!msg) {
    msg = { id: message_id, content: "", done: false, annotation_matcher };
    step.messages.push(msg);
  }

  return msg;
}

function parseContentToStep(contentArr: any[]): MessageStep {
  const draft: any = {
    steps: [],
    thinking: { status: "finished", content: "", startTime: 0, endTime: 0 },
  };

  for (const item of contentArr) {
    switch (item.type) {
      case "CUSTOM":
        if (item.name === "MAGIC_READY") {
          draft.id = item.value.answer_id;
          draft.role = "assistant";
        }
        break;

      case "THINKING_MESSAGE_START": {
        draft.thinking = {
          status: "started",
          content: "",
          startTime: item.timestamp,
        };
        break;
      }

      case "THINKING_MESSAGE_CONTENT": {
        if (item.step_id) {
          const step = resolveStep(draft, item);
          const msg = resolveMessage(step, item.message_id);

          msg.content = item.content;
        } else {
          draft.thinking.content = item.content;
        }
        break;
      }

      case "THINKING_MESSAGE_END": {
        draft.thinking.status = "finished";
        draft.thinking.endTime = item.timestamp;
        break;
      }
      case "STEP_STARTED": {
        resolveStep(draft, item);
        draft.isStepMsg = true;
        break;
      }

      case "CODE_MESSAGE_START":
      case "TEXT_MESSAGE_START": {
        const step = resolveStep(draft, item);

        resolveMessage(step, item.message_id, item.annotation_matcher);
        break;
      }

      case "CODE_MESSAGE_CONTENT":
      case "TEXT_MESSAGE_CONTENT": {
        const step = resolveStep(draft, item);
        const msg = resolveMessage(step, item.message_id);

        msg.content = item.content;
        break;
      }

      case "CODE_MESSAGE_END":
      case "TEXT_MESSAGE_END": {
        const step = resolveStep(draft, item);
        const msg = step.messages.find((m: any) => m.id === item.message_id);

        if (msg) {msg.done = true;}
        break;
      }

      case "OBJECT_MESSAGE_CONTENT": {
        const step = resolveStep(draft, item);
        const msg = resolveMessage(step, item.message_id);

        let obj: any = item.content;

        if (typeof item.content === "string") {
          try {
            obj = JSON.parse(item.content);
          } catch {
            obj = {};
          }
        }
        if (obj && typeof obj === "object") {
          const isVegaLite = obj.chart_spec_type == "vega";

          if (typeof obj.chart_config === "string") {
            obj.data = [JSON.parse(obj.chart_config)];
          }
          if (isVegaLite) {
            msg.content = `\`\`\`vega\n${  JSON.stringify(obj.data)  }\n\`\`\``;
          } else {
            msg.content = item.content;
          }
        } else {
          msg.content = item.content;
        }
        break;
      }

      case "FILE_CONTENT": {
        const step = resolveStep(draft, item);

        step.files.push({
          file_id: item.file_id,
          name: item.name,
          uri: item.uri,
          mime_type: item.mime_type,
          view_mode: Array.isArray(item.view_mode)
            ? item.view_mode
            : (item.view_mode || "").split(","),
          content: item.content,
        });
        break;
      }

      case "ANNOTATION_CONTENT": {
        const step = resolveStep(draft, item);

        if (step && Array.isArray(item.content)) {
          const msg = step.messages.find((m: any) => m.id === item.parent_message_id);

          if (msg) {
            msg.annotations = item.content;
          }
        }
        break;
      }

      case "STEP_FINISHED": {
        const step = findStepById(draft.steps, item.step_id);

        if (step) {step.status = "finished";}
        break;
      }

      case "STEP_ERROR": {
        const step = findStepById(draft.steps, item.step_id);

        if (step) {
          step.status = "error";
          step.error = item.message;
        }
        break;
      }

      case "END_MARK": {
        draft.isComplete = true;
        if (item.exit === "ERROR") {
          draft.error = item.error_message;
          markAllStepsStatus(draft.steps, "error");
        }
        break;
      }
    }
  }

  return draft;
}

/**
 * 格式化内容历史数据
 * @param content 内容数组
 * @returns 格式化后的内容对象，包含文本、引用、文件等分类数据
 */
export const contentHistoryFmt = (content?: string) => {
  if (!content)
    {return {
      isContent: false,
      textContent: [],
      references: [],
      files: [],
    };}
  const contentFmt = JSON.parse(content) as Content[];

  // 按类型分类内容
  const texts = contentFmt.filter((item: any) => item.type === "TEXT_MESSAGE_CONTENT");
  const references = contentFmt
    .filter((item: any) => item.type === ContentType.Reference)
    .map((item: any) => ({ ...item }));
  const files = contentFmt
    .filter((item: any) => item.type === ContentType.File)
    .map((item: any) => ({ ...item }));

  // 判断是否有有效内容（至少有文本内容）
  const isContent = !!texts.length;

  // 提取文本内容
  const textContent = texts?.map((item: any) => item.content);

  return {
    isContent,
    textContent,
    references,
    files,
  };
};

export function deduplicateArray(arr: ChatMessage[], id: string | null | undefined) {
  const deduplicatedMap = new Map();

  // Loop through the array and store the last occurrence of each message_id or messageId in the map

  for (const obj of arr) {
    const key = obj.id || "template-answer";

    if ((id && key === id) || key === "template-answer") {
      deduplicatedMap.delete("template-answer");
    }

    deduplicatedMap.set(key, obj);
  }

  // Convert the map values back to an array to get the deduplicated result
  const result = Array.from(deduplicatedMap.values());

  return result;
}

/**
 * 下载文件
 * @param url 文件URL
 * @param filename 文件名
 */
export const downloadFile = (url: string, filename: string) => {
  try {
    // 创建一个临时的 a 标签来触发下载
    const link = document.createElement("a");

    link.href = url;
    link.download = filename;
    link.target = "_blank";
    link.rel = "noopener noreferrer";

    // 添加到 DOM 并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
  } catch (error) {
    console.error("文件下载失败:", error);
    // 如果下载失败，尝试在新窗口打开
    window.open(url, "_blank", "noopener,noreferrer");
  }
};

/**
 * 根据文件扩展名判断文件类型
 * @param filename 文件名
 * @returns 文件类型
 */
export const getFileTypeFromName = (filename: string): string => {
  const extension = filename.split(".").pop()?.toLowerCase() || "";

  const typeMap: Record<string, string> = {
    pdf: "PDF",
    doc: "Word",
    docx: "Word",
    xls: "Excel",
    xlsx: "Excel",
    ppt: "PowerPoint",
    pptx: "PowerPoint",
    txt: "Text",
    md: "Markdown",
    csv: "CSV",
    jpg: "Image",
    jpeg: "Image",
    png: "Image",
    gif: "Image",
    svg: "Image",
    zip: "Archive",
    rar: "Archive",
    "7z": "Archive",
    mp4: "Video",
    avi: "Video",
    mov: "Video",
    mp3: "Audio",
    wav: "Audio",
    flac: "Audio",
  };

  return typeMap[extension] || extension.toUpperCase() || "Unknown";
};

// 获取csv数据
export const fetchCSV = async (url: string) => {
  try {
    const res = await fetch(url);

    if (res.ok) {
      // 读取csv文件，以对象数组的形式返回
      const csvContent = await res.text();
      const records = csvContent.split("\n");

      return records.map((record) => record.split(","));
    }
  } catch (error) {
    throw new Error(`Error: ${error}`);
  }
};

export const previewFileData = async (url: string, maxRows: number, maxColumns: number) => {
  try {
    const baseUrl = process.env.WEB_API_URL || "";
    const api = `${baseUrl}/api/v1/portal-ragtop/file/table_preview`;
    const token = localStorage.getItem("access_token");

    const res = await fetch(api, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        url,
        max_rows: maxRows,
        max_columns: maxColumns,
      }),
    });

    const fileData = await res.json();

    if (fileData.code === "SUCCESS") {
      return fileData.data;
    }

    return fileData;
  } catch (error) {
    throw new Error(`Error: ${error}`);
  }
};

export const buildColumns = (headerRow: string[]) => {
  const helper = createColumnHelper<Record<string, any>>();

  return headerRow.map((colName) =>
    helper.accessor(colName, {
      header: colName,
      cell: (info) => info.getValue(),
    })
  );
};

export const buildData = (rows: string[][]) => {
  const header = rows[0];

  if (header) {
    return rows.slice(1).map((cells) => Object.fromEntries(header.map((k, i) => [k, cells[i]])));
  }
};

// 递归设置所有 step 的状态
function markAllStepsStatus(steps: MessageStep[], status: MessageStep["status"]) {
  for (const step of steps) {
    if (step.status !== "finished") {
      step.status = status;
    }
    if (step.subSteps && step.subSteps.length > 0) {
      markAllStepsStatus(step.subSteps, status);
    }
  }
}
