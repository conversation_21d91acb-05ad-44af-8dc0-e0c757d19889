import { visit } from "unist-util-visit";

/**
 * Remark 插件：将 ##数字$$ 格式转换为 quote directive 节点
 * 这样可以利用 remark-directive 来处理引用
 */
function remarkReferencePlugin() {
  return (tree: any) => {
    visit(tree, "text", (node, index, parent) => {
      // 匹配 ##数字$$ 格式
      const referenceRegex = /##(\d+)\$\$/g;
      let match;
      let lastIndex = 0;
      const parts: any[] = [];

      // 重置正则表达式的 lastIndex
      referenceRegex.lastIndex = 0;

      while ((match = referenceRegex.exec(node.value)) !== null) {
        // 添加引用前的文本
        if (match.index > lastIndex) {
          parts.push({
            type: "text",
            value: node.value.slice(lastIndex, match.index),
          });
        }

        // 创建 quote directive 节点
        const refIndex = parseInt(match[1] || "0", 10);

        parts.push({
          type: "textDirective",
          name: "quote",
          attributes: {},
          children: [
            {
              type: "text",
              value: refIndex.toString(),
            },
          ],
        });

        lastIndex = match.index + match[0].length;
      }

      // 添加剩余的文本
      if (lastIndex < node.value.length) {
        parts.push({
          type: "text",
          value: node.value.slice(lastIndex),
        });
      }

      // 如果找到了引用，替换原节点
      if (parts.length > 1) {
        // 将原节点替换为 parts 数组
        Object.assign(node, parts[0]);

        // 将剩余的 parts 插入到父节点的 children 中，在原节点之后
        if (parent && parent.children && typeof index === "number") {
          parent.children.splice(index + 1, 0, ...parts.slice(1));
        }
      }
    });
  };
}

export default remarkReferencePlugin;
