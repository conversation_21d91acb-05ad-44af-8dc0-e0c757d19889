import { formatMsgLocalTime } from "@/lib/chat-utils";
import { API_PREFIX } from "@/service/common";
import {
  ChatMessage,
  FileContent,
  ReadyContnet,
  ReferenceContent,
} from "@/service/session-service";
import { getTeamId } from "@/store/team-store";
import { useCallback, useEffect, useRef, useState } from "react";
import { v4 as uuid } from "uuid";

export type StreamResponse = {
  id: string;
  model: string;
  created: number;
  choices: {
    delta: {
      content?: string | FileContent | ReferenceContent | ReadyContnet;
    };
    finish_reason: string | null;
    index: number;
  }[];
};

export interface MessageStep {
  id: string; // step_id 或 'default'
  name: string; // 步骤名
  status: "paused" | "started" | "finished" | "error";
  messages: {
    id: string; // message_id
    content: string;
    done: boolean;
    annotation_matcher?: any[];
    annotations?: any[];
  }[];
  files: LocalFileContent[];
  subSteps?: MessageStep[]; // 新增：子步骤
}

interface LocalFileContent {
  file_id: string;
  name: string;
  uri: string;
  mime_type: string;
  view_mode: string[];
  content?: string;
}

export interface Message {
  id?: string; // answer_id
  role?: string;
  steps: MessageStep[];
  createTime?: string;
  isComplete: boolean;
  error?: string;
  thinking: any;
  msgAutoOpen?: boolean;
  isStepMsg?: boolean; // 是否是分布回答的消息
}

function parseSSEData(line: string): { event?: string; data?: StreamResponse } | null {
  try {
    const trimmed = line.trim();

    if (trimmed.startsWith("event:")) {
      return { event: trimmed.slice(6).trim() };
    }
    if (trimmed.startsWith("data:")) {
      const dataStr = trimmed.slice(5).trim();

      if (dataStr === "[DONE]") {return null;}

      return { data: JSON.parse(dataStr) };
    }
  } catch (err) {
    console.warn("Failed to parse SSE data line:", line, err);
  }

  return null;
}

type SendMessageFn = (
  data: ChatMessage,
  onChunk: (chunk: StreamResponse, eventType?: string) => void,
  onComplete?: () => void,
  onError?: (error: string) => void
) => AbortController;

export const useChatMessageState = (sendMessage: SendMessageFn) => {
  const [message, setMessage] = useState<Message>({
    steps: [],
    isComplete: false,
    thinking: { status: "started", content: "", startTime: 0, endTime: 0 },
  });

  // 1. 用 ref 保存最新消息
  const messageRef = useRef<Message>(message);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const controllerRef = useRef<AbortController | null>(null);
  const answerIdRef = useRef<string | null>(null);

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 防止没有提问的时候，也会被setInterval
    if (!isStreaming) {
      return;
    }
    timerRef.current = setInterval(() => {
      requestAnimationFrame(() => setMessage({ ...messageRef.current }));
    }, 150);

    return () => {
      if (timerRef.current) {clearInterval(timerRef.current);}
    };
  }, [isStreaming]);

  const stopAnswer = async () => {
    const baseUrl = process.env.WEB_API_URL || "";
    const apiPrefix = `${API_PREFIX}/agent-session`;
    const url = `${baseUrl}${apiPrefix}/chat-cancel`;

    const token = localStorage.getItem("access_token");
    const requestData = {
      answer_id: answerIdRef.current,
    };

    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      // 处理错误
      throw new Error("Network response was not ok");
    }
    const data = await response.json();

    return data;
  };

  // 构造steps的结构
  // 用于不是分布回答的回答 也构造出一样的数据结构 steps: []
  // 还得兼容没有 thinking 的提问
  function getOrCreateStep(
    draft: Message,
    step_id?: string,
    name?: string,
    parentStep?: MessageStep
  ): MessageStep {
    let step: MessageStep;

    if (step_id) {
      draft.isStepMsg = true;
      if (parentStep) {
        if (!parentStep.subSteps) {parentStep.subSteps = [];}
        const found = parentStep.subSteps.find((s) => s.id === step_id);

        if (found) {
          step = found;
        } else {
          step = {
            id: step_id,
            name: name || "未命名步骤",
            status: "started",
            messages: [],
            files: [],
            subSteps: [],
          };
          parentStep.subSteps.push(step);
        }

        return step;
      } else {
        const found = draft.steps.find((s) => s.id === step_id);

        if (found) {
          step = found;
        } else {
          step = {
            id: step_id,
            name: name || "未命名步骤",
            status: "started",
            messages: [],
            files: [],
            subSteps: [],
          };
          draft.steps.push(step);
        }

        return step;
      }
    } else {
      draft.isStepMsg = false;
      if (draft.steps.length === 0) {
        step = {
          id: `default` + `-${  uuid()}`,
          name: "完整回答",
          status: "started",
          messages: [],
          files: [],
          subSteps: [],
        };
        draft.steps.push(step);
      } else {
        step = draft.steps[0] ?? {
          id: `default` + `-${  uuid()}`,
          name: "完整回答",
          status: "started",
          messages: [],
          files: [],
          subSteps: [],
        };
        if (draft.steps.length === 0) {draft.steps.push(step);}
      }
      draft.isStepMsg = false;

      return step;
    }
  }

  // 辅助函数：递归查找 step_id
  function findStepById(steps: MessageStep[], step_id: string): MessageStep | undefined {
    for (const step of steps) {
      if (step.id === step_id) {return step;}
      if (step.subSteps && step.subSteps.length > 0) {
        const found = findStepById(step.subSteps, step_id);

        if (found) {return found;}
      }
    }

    return undefined;
  }

  // 辅助函数：根据 chunk 获取正确的 step 或 subStep
  function resolveStep(draft: Message, chunk: any): MessageStep {
    if (chunk.parent_step_id) {
      // 递归查找 parent_step_id 是否在多级 subSteps 中
      const parentStep = findStepById(draft.steps, chunk.parent_step_id);

      if (parentStep) {
        // 父 step 已存在，放到 subSteps
        return getOrCreateStep(draft, chunk.step_id, chunk.name, parentStep);
      } else {
        // 父 step 不存在，直接作为顶层 step
        return getOrCreateStep(draft, chunk.step_id, chunk.name);
      }
    } else if (chunk.step_id) {
      // 兼容：step_id 可能是 subStep，但没有 parent_step_id
      let step = draft.steps.find((s) => s.id === chunk.step_id);

      if (!step) {
        step = findStepById(draft.steps, chunk.step_id);
      }
      if (step) {return step;}

      return getOrCreateStep(draft, chunk.step_id, chunk.name);
    } else {
      return getOrCreateStep(draft);
    }
  }

  // 辅助函数：查找或创建 message
  function resolveMessage(step: MessageStep, message_id: string, annotation_matcher?: any[]): any {
    let msg = step.messages.find((m) => m.id === message_id);

    if (!msg) {
      msg = { id: message_id, content: "", done: false, annotation_matcher };
      step.messages.push(msg);
    }

    return msg;
  }

  // startStream、cancelStream、clearMessage 应为 useCallback
  const startStream = useCallback(
    (data: ChatMessage) => {
      controllerRef.current?.abort();

      // 重置状态
      const initialMessage: Message = {
        id: "template-answer",
        role: "assistant",
        steps: [],
        isComplete: false,
        thinking: {},
      };

      setMessage(initialMessage);
      messageRef.current = initialMessage; // 同步更新 ref

      setError(null);
      setIsStreaming(true);

      const controller = sendMessage(
        data,
        (chunk: any) => {
          // 直接更新 messageRef.current，不使用 draft
          messageRef.current.createTime = formatMsgLocalTime(Date.now());
          messageRef.current.msgAutoOpen = true;

          switch (chunk.type) {
            case "CUSTOM":
              if (chunk.name === "MAGIC_READY") {
                messageRef.current.id = chunk.value.answer_id;
                messageRef.current.role = "assistant";
                answerIdRef.current = chunk.value.answer_id;
              }
              break;

            case "THINKING_MESSAGE_START": {
              messageRef.current.thinking = {
                status: "started",
                content: "",
                startTime: chunk.timestamp,
              };
              break;
            }

            case "THINKING_MESSAGE_CHUNK": {
              if (chunk.step_id) {
                const step = resolveStep(messageRef.current, chunk);
                const msg = resolveMessage(step, chunk.message_id);

                msg.content += chunk.delta;
              } else {
                messageRef.current.thinking.content += chunk.delta;
              }
              break;
            }

            case "THINKING_MESSAGE_CONTENT": {
              if (chunk.step_id) {
                const step = resolveStep(messageRef.current, chunk);
                const msg = resolveMessage(step, chunk.message_id);

                msg.content += chunk.content;
              }
              break;
            }

            case "THINKING_MESSAGE_END": {
              messageRef.current.thinking.status = "finished";
              messageRef.current.thinking.endTime = chunk.timestamp;
              break;
            }

            case "STEP_STARTED": {
              resolveStep(messageRef.current, chunk);
              break;
            }

            case "CODE_MESSAGE_START":
            case "TEXT_MESSAGE_START": {
              const step = resolveStep(messageRef.current, chunk);

              resolveMessage(step, chunk.message_id, chunk.annotation_matcher);
              break;
            }

            case "CODE_MESSAGE_CONTENT":
            case "TEXT_MESSAGE_CONTENT": {
              const step = resolveStep(messageRef.current, chunk);
              const msg = resolveMessage(step, chunk.message_id);

              msg.content = chunk.content;
              break;
            }

            case "CODE_MESSAGE_CHUNK":
            case "TEXT_MESSAGE_CHUNK": {
              const step = resolveStep(messageRef.current, chunk);
              const msg = resolveMessage(step, chunk.message_id);

              msg.content += chunk.delta;
              break;
            }

            case "CODE_MESSAGE_END":
            case "TEXT_MESSAGE_END": {
              const step = resolveStep(messageRef.current, chunk);
              const msg = step.messages.find((m) => m.id === chunk.message_id);

              if (msg) {msg.done = true;}
              break;
            }

            case "OBJECT_MESSAGE_CONTENT": {
              const step = resolveStep(messageRef.current, chunk);
              const msg = resolveMessage(step, chunk.message_id);

              let obj: any = chunk.content;

              if (typeof chunk.content === "string") {
                try {
                  obj = JSON.parse(chunk.content);
                } catch {
                  obj = {};
                }
              }
              // 判断是否为 vega-lite 配置
              const isVegaLite = obj.chart_spec_type == "vega";

              if (typeof obj.chart_config === "string") {
                obj.data = [JSON.parse(obj.chart_config)];
              }
              if (isVegaLite) {
                msg.content = `\`\`\`vega\n${  JSON.stringify(obj.data)  }\n\`\`\``;
              } else {
                msg.content = chunk.content;
              }
              break;
            }

            case "FILE_CONTENT": {
              const step = resolveStep(messageRef.current, chunk);

              step.files.push({
                file_id: chunk.file_id,
                name: chunk.name,
                uri: chunk.uri,
                mime_type: chunk.mime_type,
                view_mode: Array.isArray(chunk.view_mode)
                  ? chunk.view_mode
                  : (chunk.view_mode || "").split(","),
                content: chunk.content,
              });
              break;
            }

            case "ANNOTATION_CONTENT": {
              const step = resolveStep(messageRef.current, chunk);

              if (step && Array.isArray(chunk.content)) {
                const msg = step.messages.find((m) => m.id === chunk.parent_message_id);

                if (msg) {
                  msg.annotations = chunk.content;
                }
              }
              break;
            }

            case "STEP_FINISHED": {
              const step = findStepById(messageRef.current.steps, chunk.step_id);

              if (step) {step.status = "finished";}
              break;
            }

            case "STEP_ERROR": {
              const step = findStepById(messageRef.current.steps, chunk.step_id);

              if (step) {
                step.status = "error";
                step.error = chunk.message;
              }
              break;
            }

            case "END_MARK": {
              messageRef.current.isComplete = true;
              // 如果没有thinking，在endmark时，把thinking状态设置为finished
              messageRef.current.thinking.status = "finished";
              if (chunk.exit === "ERROR") {
                messageRef.current.error = chunk.error_message;
                markAllStepsStatus(messageRef.current.steps, "error");
              }
              break;
            }
          }
          // 移除 messageRef.current = draft; 因为直接修改了 messageRef.current
        },
        () => handleSSEEnd(), // 正常结束
        (err) => handleSSEEnd(err) // 异常结束
      );

      controllerRef.current = controller;
    },
    [sendMessage]
  );

  const cancelStream = useCallback(async () => {
    const result = await stopAnswer();

    if (result.code == "SUCCESS") {
      controllerRef.current?.abort();
      setIsStreaming(false);
      messageRef.current.isComplete = true;
      messageRef.current.thinking.status = "finished";
      markAllStepsStatus(messageRef.current.steps, "paused");
      setMessage(messageRef.current);
    }
  }, []);

  const clearMessage = useCallback(() => {
    const emptyMessage = {
      steps: [],
      isComplete: false,
      thinking: { status: "finished", content: "", startTime: 0, endTime: 0 },
    };

    setMessage(emptyMessage);
    messageRef.current = emptyMessage; // 同步更新 ref
    setError(null);
  }, []);

  // 在 SSE 结束（onComplete/onError）时，停止定时器
  const handleSSEEnd = (err?: string) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (err) {
      if (messageRef.current?.steps.length > 0) {
        markAllStepsStatus(messageRef.current.steps, "error");
        messageRef.current.thinking.status = "finished";
      }
      setMessage({ ...messageRef.current, error: err });
      setError(err);
    } else {
      setMessage({ ...messageRef.current });
    }
    setIsStreaming(false);
  };

  // 递归设置所有 step 的状态
  function markAllStepsStatus(steps: MessageStep[], status: MessageStep["status"]) {
    for (const step of steps) {
      if (step.status !== "finished") {
        step.status = status;
      }
      if (step.subSteps && step.subSteps.length > 0) {
        markAllStepsStatus(step.subSteps, status);
      }
    }
  }

  return {
    message,
    isStreaming,
    error,
    startStream,
    cancelStream,
    clearMessage,
  };
};

export const useSendMessageStream = () => {
  // const queryClient = useQueryClient();

  const sendMessage = (
    data: ChatMessage,
    onChunk: (chunk: StreamResponse, eventType?: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void
  ): AbortController => {
    const controller = new AbortController();

    (async () => {
      try {
        const baseUrl = process.env.WEB_API_URL || "";
        const apiPrefix = `${API_PREFIX}/agent-session`;
        const url = `${baseUrl}${apiPrefix}/chat-completion`;

        const token = localStorage.getItem("access_token");
        const teamId = getTeamId();
        const requestData = {
          ...data,
          team_id: teamId || undefined,
        };

        const response = await fetch(url, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "text/event-stream",
            "Cache-Control": "no-cache",
          },
          body: JSON.stringify(requestData),
          signal: controller.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error("Response body is not readable");
        }

        let buffer = "";
        const currentEventType: string | undefined = undefined;

        while (true) {
          const { done, value } = await reader.read();

          if (done) {break;}

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");

          buffer = lines.pop() || "";

          for (const line of lines) {
            const parsed: any = parseSSEData(line);

            if (!parsed) {
              continue;
            }
            if (parsed.data) {
              onChunk(parsed.data, parsed?.data?.type);
              if (parsed.data.type == "END_MARK") {
                onComplete?.();

                return;
              }
            }
          }
        }

        onComplete?.();
        // queryClient.invalidateQueries({
        //   queryKey: ["sessions", data.session_id, "history"],
        // });
      } catch (error: any) {
        if (!controller.signal.aborted) {
          const errorMessage = error instanceof Error ? error.message : "发送消息失败";

          onError?.(errorMessage);
        }
      }
    })();

    return controller;
  };

  return { sendMessage };
};
