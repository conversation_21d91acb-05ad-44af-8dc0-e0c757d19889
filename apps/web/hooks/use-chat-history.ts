/**
 * 聊天历史分页加载 Hook
 *
 * 支持向上滚动加载更多历史消息，自动去重，倒序排列
 */

import { convertHistoryToMessages } from "@/lib/chat-utils";
import { useSessionHistory } from "@/service/session-service";
import { useCallback, useEffect, useRef, useState } from "react";

interface UseChatHistoryConfig {
  sessionId: string;
  pageSize?: number;
  enabled?: boolean;
}

interface UseChatHistoryReturn {
  messages: any[];
  isLoading: boolean;
  hasMore: boolean;
  loadMore: () => void;
  refresh: () => void;
  total: number;
}

/**
 * 聊天历史分页加载 Hook
 */
export function useChatHistory({
  sessionId,
  pageSize = 20,
  enabled = true,
}: UseChatHistoryConfig): UseChatHistoryReturn {
  const [messages, setMessages] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  // 请求状态引用 - 用于防止重复请求
  const requestRef = useRef<{
    isRequesting: boolean;
    lastPage: number;
  }>({
    isRequesting: false,
    lastPage: 0,
  });

  // 获取当前页的历史消息
  const {
    data: sessionHistory,
    isLoading,
    refetch,
  } = useSessionHistory(
    sessionId,
    currentPage,
    pageSize,
    enabled && !requestRef.current.isRequesting
  );

  /**
   * 处理历史消息数据更新
   */
  useEffect(() => {
    if (!sessionHistory || !sessionHistory.records) {
      return;
    }

    const newMessages = convertHistoryToMessages(sessionHistory.records);

    // 更新总数
    setTotal(sessionHistory.total || 0);

    // 判断是否还有更多数据
    const currentCount = currentPage * pageSize;

    setHasMore(currentCount < (sessionHistory.total || 0));

    if (currentPage === 1) {
      // 第一页，直接设置消息
      setMessages(newMessages);
    } else {
      // 后续页面，合并消息并去重
      setMessages((prevMessages) => {
        // 将新消息添加到开头（因为历史消息是倒序的）
        const combinedMessages = [...newMessages, ...prevMessages];

        // 根据 id 去重
        const uniqueMessages = deduplicateChatMessages(combinedMessages);

        return uniqueMessages;
      });
    }

    // 更新请求状态
    requestRef.current.lastPage = currentPage;
    requestRef.current.isRequesting = false;
  }, [sessionHistory, currentPage, pageSize]);

  /**
   * 加载更多历史消息
   */
  const loadMore = useCallback(() => {
    if (!hasMore || isLoading || requestRef.current.isRequesting) {
      return;
    }

    requestRef.current.isRequesting = true;
    setCurrentPage((prev) => prev + 1);
  }, [hasMore, isLoading]);

  /**
   * 刷新历史消息
   */
  const refresh = useCallback(() => {
    setCurrentPage(1);
    setMessages([]);
    setHasMore(true);
    setTotal(0);
    requestRef.current = {
      isRequesting: false,
      lastPage: 0,
    };
    refetch();
  }, [refetch]);

  return {
    messages,
    isLoading,
    hasMore,
    loadMore,
    refresh,
    total,
  };
}

/**
 * 去重函数的扩展版本，支持聊天消息
 */
export function deduplicateChatMessages(messages: any[]): any[] {
  const seen = new Set<string>();
  const result: any[] = [];

  for (const message of messages) {
    const {id} = message;

    if (id && !seen.has(id)) {
      seen.add(id);
      result.push(message);
    } else if (!id) {
      // 如果没有 id，直接添加（可能是临时消息）
      result.push(message);
    }
  }

  return result;
}
