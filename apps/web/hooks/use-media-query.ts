"use client";

import { useState, useEffect } from "react";

/**
 * 媒体查询钩子
 * @param query 媒体查询字符串
 * @returns 是否匹配
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);

    // 初始化匹配状态
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    // 监听变化
    const listener = () => setMatches(media.matches);

    media.addEventListener("change", listener);

    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
}
