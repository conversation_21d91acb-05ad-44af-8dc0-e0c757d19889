/**
 * 认证服务
 *
 * 提供登录、登出和token管理功能
 */

import {
  clearAccessToken,
  createWebApiClient,
  PaginatedResponse,
  setAccessToken,
} from "@/lib/api/web-api-client";
import { currentUserAtom, initTeamFromUserAtom, teamListAtom } from "@/store/team-store";
import { useMutation } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { API_PREFIX } from "./common";
import { User } from "./team-service";

// 创建API客户端，设置API前缀
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/user`,
  });

// 登录响应接口
export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: {
    id: string;
    name: string;
    role: string;
  };
  teams?: {
    id: string;
    title: string;
    roles?: string[];
  }[];
}

// API响应中的用户数据结构
interface ApiUser {
  user: {
    id: string;
    nick?: string;
    username?: string;
  };
  joined: boolean;
}

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 修改密码接口
export interface PasswordModifyRequest {
  new_password: string;
}

/**
 * 获取当前用户信息
 */
export const useCurrentUser = () => {
  const setTeams = useSetAtom(teamListAtom);
  const setCurrentUser = useSetAtom(currentUserAtom);
  const initTeamFromUser = useSetAtom(initTeamFromUserAtom);

  return useMutation({
    mutationFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<User>("/current");
    },
    onSuccess: (user: User) => {
      // 保存用户信息到Jotai状态
      setCurrentUser(user);

      if (user.teams && user.teams.length > 0) {
        // 保存团队列表
        setTeams(user.teams); // 初始化当前团队（选择第一个团队或已保存的团队）
        initTeamFromUser(user.teams);
      }
    },
  });
};

/**
 * 用户登录
 */
export const useLogin = () => {
  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<LoginResponse>("/signin", credentials);

      // 保存token到localStorage
      if (response.access_token) {
        setAccessToken(response.access_token);
      }

      return response;
    },
  });
};

/**
 * 用户登出
 */
export const useLogout = () => {
  // 获取Jotai的清理函数
  // const clearAuthState = useSetAtom(clearAuthStateAtom);

  return useMutation({
    mutationFn: async () => {
      // 调用登出API
      try {
        const apiClient = await getApiClient();

        await apiClient.post("/signout");
      } catch (error) {
        console.error("登出API调用失败", error);
      }

      // 无论API是否成功，都清除所有本地存储的数据
      clearAccessToken();

      // 清除所有认证相关的localStorage项
      // 不清除的话换个账号登录就会有问题
      if (typeof window !== "undefined") {
        localStorage.removeItem("current_user");
        localStorage.removeItem("current_team");
      }

      return true;
    },
  });
};

/**
 * 检查用户是否已登录
 */
export const isAuthenticated = (): boolean => {
  if (typeof window === "undefined") {return false;}

  return !!localStorage.getItem("access_token");
};

/**
 * 获取可添加的团队成员列表（分页查询）
 *
 * @param params - 查询参数，包括页码、每页数量和关键词
 */
export const useAvailableMembers = (pageNumber = 1, pageSize = 10) => {
  return useMutation({
    mutationFn: async (params: { keyword?: string }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<ApiUser>>(
        "/query-memberships",
        params,
        {
          isPaginated: true,
          pageNumber,
          pageSize,
          requireTeamId: true,
        }
      );

      return response;
    },
  });
};

/**
 * 获取可添加的团队成员列表（支持动态分页参数）
 */
export const useAvailableMembersWithDynamicPaging = () => {
  return useMutation({
    mutationFn: async (params: { keyword?: string; pageNumber?: number; pageSize?: number }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<ApiUser>>(
        "/query-memberships",
        {
          keyword: params.keyword,
        },
        {
          isPaginated: true,
          pageNumber: params.pageNumber || 1,
          pageSize: params.pageSize || 10,
          requireTeamId: true,
        }
      );

      return response;
    },
  });
};

// /**
//  * 获取当前账号所在的团队列表
//  */
// export const useCurrentTeams = () => {
//   return useMutation({
//     mutationFn: async () => {
//       const response = await apiClient.post('/query-memberships', {}, {
//         isPaginated: false,
//         requireTeamId: true
//       })
//       return response
//     }
//   })
// }

/**
 * 修改密码
 */
export const usePasswordModify = () => {
  return useMutation({
    mutationFn: async (password: PasswordModifyRequest) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post("/current/modify-password", password);

      return response;
    },
  });
};
