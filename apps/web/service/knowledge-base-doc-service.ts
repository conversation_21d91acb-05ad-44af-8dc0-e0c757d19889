/**
 * 知识库服务
 *
 * 提供知识库相关的API请求方法
 */

import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-api-client";
import { parseMethodAtom } from "@/store/kbase-store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { API_PREFIX } from "./common";

// 创建API客户端
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/kbase-doc`,
  });

// API基础路径

// 知识库接口
export interface KnowledgeBaseDoc {
  kbase_id: string;
  keyword?: string[];
  document_id: string;
}

/**
 * ModifyDocChunkingRequest
 */
export interface ModifyDocChunking {
  chunk_config: ChunkConfig;
  document_id?: string;
  team_id?: string;
  [property: string]: any;
}

/**
 * ChunkConfig
 */
export interface ChunkConfig {
  chunk_provider_id?: string;
  /**
   * 暂不支持
   */
  pdf_pages?: PageRange[];
  pdf_parser_config?: PdfParserConfig;
  [property: string]: any;
}

/**
 * PageRange，暂不支持
 */
export interface PageRange {
  end?: number;
  start?: number;
  [property: string]: any;
}

/**
 * PdfParserConfig
 */
export interface PdfParserConfig {
  parser_provider_id?: string;
  [property: string]: any;
}

/**
 * KbaseChunkConfig
 */
export interface KbaseChunkConfig {
  chunk_provider_id?: string;
  [property: string]: any;
}

/**
 * KbasePdfParserConfig
 */
export interface KbasePdfParserConfig {
  parser_provider_id?: string;
  [property: string]: any;
}

/**
 * 运行状态
 */
export enum RunStatus {
  Cancelling = "CANCELLING",
  Cancel = "CANCEL",
  Done = "DONE",
  Fail = "FAIL",
  Running = "RUNNING",
  Unstart = "UNSTART",
}

// 知识库详情接口
export interface KnowledgeBaseDocList {
  chunk_config?: ChunkConfig;
  create_time?: Date;
  /**
   * 启用状态
   */
  enable_status?: string;
  id: string;
  name: string;
  /**
   * 运行状态
   */
  run_status: RunStatus;
  size?: number;
  type?: string;
  chunk_num?: number;
  [property: string]: any;
}

/**
 * 获取不同文档类型支持的解析方法列表
 */

type ParserConfig = {
  parse_model: string;
  file_extensions: string[];
};

export const useListParseModels = (enabled = true) => {
  const setParseMethod = useSetAtom(parseMethodAtom);

  return useQuery({
    queryKey: ["kbase-parse-list"],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<ParserConfig[]>("/list-parse-models", {});
    },
    select: (data) => {
      setParseMethod(data);
    },
    enabled,
  });
};

/**
 * 获取知识库详情列表
 * TODO: 分页查询
 */
export const useKnowledgeBaseDoc = (id = "", pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["kbaseDoc", pageNumber, pageSize, id],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<PaginatedResponse<KnowledgeBaseDocList>>(
        "/query",
        { kbase_id: id },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    refetchInterval: ({ state }) => {
      const result = state?.data?.records;
      const hasRunning = result?.find(({ run_status }: KnowledgeBaseDocList) =>
        [RunStatus.Running, RunStatus.Cancelling].includes(run_status)
      );

      return hasRunning ? 15000 : false; // 返回 false 表示停止轮询
    },
    enabled: !!id && enabled,
    gcTime: 0,
  });
};

/**
 * 启用文档
 */
export const useKBaseDocEnable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { document_id: string }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/enable`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 禁用文档
 * @returns
 */
export const useKBaseDocDisabled = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { document_id: string }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/disable`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 开始解析
 * @returns
 */

export const useKBaseDocStartParse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { document_ids: string[] }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/batch-start-parse`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 终止解析
 * @returns
 */
export const useKBaseDocStopParse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { document_ids: string[] }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/batch-stop-parse`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 将文档从知识库中移除
 * @returns
 */
export const useKBaseDocDelete = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { document_ids: string[] }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/batch-delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
      queryClient.invalidateQueries({ queryKey: ["knowledgeBases"] });
    },
  });
};

/**
 * 修改切片方法
 */

export const useKBaseDocModifyChunking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ModifyDocChunking) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/modify-chunking-config`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};
