/**
 * 站点配置服务
 *
 * 提供站点配置相关的API请求方法
 */

import { createWebApiClient } from "@/lib/api/web-api-client";
import { siteConfigAtom } from "@/store/user-store";
import { useQuery } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { API_PREFIX } from "./common";

// 创建API客户端
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}`,
  });

export interface TenantSiteConfig {
  favicon?: string;
  id?: string;
  logo?: string;
  mini_logo?: string;
  name?: string;
  tenant_id?: string;
  [property: string]: any;
}

export interface SiteConfigRequest {
  favicon?: string;
  logo?: string;
  mini_logo?: string;
  name?: string;
  [property: string]: any;
}

/**
 * 获取站点配置
 */
export const useSiteConfig = () => {
  const setSiteConfig = useSetAtom(siteConfigAtom);

  return useQuery({
    queryKey: ["siteConfig"],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<TenantSiteConfig>("/tenant/describe-site-config");
    },
    select: (data) => {
      setSiteConfig(data);
    },
    refetchOnMount: true,
  });
};
