/**
 * 模型服务
 *
 * 提供模型配置相关的API请求方法
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_PREFIX } from "./common";

import { createWebApiClient } from "@/lib/api/web-api-client";

// 创建API客户端
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}`,
  });

// 模型接口
export interface ConfiguredModel {
  llm_factory_id: string;
  llm_name: string;
  llm_id: string;
  model_type: string;
  api_key: string;
  api_base?: string;
  base_url?: string;
}

export interface Model {
  id: string;
  name: string;
  logo: string;
  tags: string;
}

export enum ModelType {
  Chat = "CHAT",
  Embedding = "EMBEDDING",
  Rerank = "RERANK",
  NL2SQL = "NL2SQL",
  NL2PYTHON = "NL2PYTHON",
}

export interface ConfiguredModelList {
  api_base: string;
  llm_factory: string;
  llm_id: string;
  llm_name: string;
  llm_alias: string;
  model_purpose: ModelType;
  temperature?: number;
  top_p?: number;
  model_type?: ModelType;
}

export interface ConfigurabledModel {
  llm_factory: string;
  llm_id: string;
  llm_name: string;
  model_type?: ModelType;
}

export interface LlmSettings {
  chat_model?: string;
  embd_model?: string;
  rerank_model?: string;
  nl2sql_model?: string;
  nl2python_model?: string;
}

/**
 * 获取已有的模型列表
 */
export const useExistingModels = (enable = false) => {
  return useQuery({
    queryKey: ["existingModels"],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<ConfiguredModelList[]>(`/llm/describe-models`);
    },
    enabled: !!enable,
  });
};

/**
 * 修改团队模型
 */
export const useLLMSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/team/modify-llm-settings`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["llmSetting"] });
    },
  });
};

/**
 * 获取当前团队模型
 */
export const useQueryLLMSettings = (enable = false) => {
  return useQuery({
    queryKey: ["llmSetting"],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<LlmSettings>(`/team/query-llm-settings`);
    },
    enabled: enable,
  });
};
