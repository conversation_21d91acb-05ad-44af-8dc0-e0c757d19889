/**
 * 用户服务
 *
 * 提供用户相关的API请求方法
 */

import { createManagementApiClient, userApiClient } from "@/lib/api/management-api-client";
import { currentUserAtom } from "@/store/user-store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";

// 使用统一的API客户端
const getApiClient = userApiClient;

// 分页专用的API客户端获取函数
const getPageApiClient = () =>
  createManagementApiClient("/user", {
    isPaginated: true,
  });

export interface CreateParams {
  loginName?: string;
  nick?: string;
  password?: string;
  [property: string]: any;
}

// 用户接口
export interface User {
  id: string;
  name: string;
  login_name?: string;
  nick?: string;
  email: string;
  role: "admin" | "user";
  createdAt: string;
}

/**
 * 获取用户列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 * @param keyword - 搜索关键词
 */
export const useUsers = (pageNumber: number = 1, pageSize: number = 10, keyword?: string) => {
  return useQuery({
    queryKey: ["users", pageNumber, pageSize, keyword],
    queryFn: async () => {
      const pageApiClient = await getPageApiClient();

      return pageApiClient.post<{
        records: User[];
        total: number;
        page_number: number;
        page_size: number;
      }>(
        "/query",
        {
          keyword,
        },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
  });
};

/**
 * 查询用户列表（用于选择器）
 */
export const useQueryUsers = () => {
  return useMutation({
    mutationFn: async (params: { page_number?: number; page_size?: number; keyword?: string }) => {
      const pageApiClient = await getPageApiClient();

      return pageApiClient.post<{
        records: User[];
        total: number;
        page_number: number;
        page_size: number;
      }>("/query", params);
    },
  });
};

/**
 * 获取当前用户
 */
export const useCurrentUser = () => {
  const setCurrentUser = useSetAtom(currentUserAtom);

  return useMutation({
    mutationFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<User>("/current");
    },
    onSuccess: (data: User) => {
      setCurrentUser(data);
    },
  });
};

/**
 * 获取用户详情
 */
export const useUser = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: "user_id") => {
      const apiClient = await getApiClient();

      return apiClient.post<User>("/get", userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

/**
 * 创建用户
 */
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: CreateParams) => {
      const apiClient = await getApiClient();

      return apiClient.post<User>("/create", userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

/**
 * 更新用户
 */
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: Partial<CreateParams> & { user_id: string }) => {
      const apiClient = await getApiClient();

      return apiClient.post<User>(`/update`, userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

/**
 * 删除用户
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { user_id: string }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};
