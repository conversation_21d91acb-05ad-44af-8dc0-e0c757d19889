/**
 * 模型服务
 *
 * 提供模型配置相关的API请求方法
 */

import { llmApiClient } from "@/lib/api/management-api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// 使用统一的API客户端
const getApiClient = llmApiClient;

// 模型接口
export interface ConfiguredModel {
  llm_factory_id: string;
  llm_name: string;
  llm_id: string;
  model_type: string;
  api_key: string;
  base_url?: string;
}

export interface Model {
  id: string;
  name: string;
  logo: string;
  tags?: string;
  model_purposes: string[];
}

export enum ModelType {
  Chat = "CHAT",
  Embedding = "EMBEDDING",
  Rerank = "RERANK",
  NL2SQL = "NL2SQL",
  NL2PYTHON = "NL2PYTHON",
}

export interface ConfiguredModelList {
  base_url: string;
  llm_alias: string;
  llm_factory: string;
  llm_id: string;
  llm_name: string;
  model_type?: ModelType;
}

export interface ConfigurabledModel {
  llm_factory: string;
  llm_id: string;
  llm_name: string;
  model_type?: ModelType;
}

/**
 * 获取已有的模型列表
 */
export const useExistingModels = () => {
  return useQuery({
    queryKey: ["existingModels"],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<ConfiguredModelList[]>(`/describe-models`);
    },
  });
};

export const useModelTypes = () => {
  const { data: configuredModels } = useExistingModels();
  const chatOption = configuredModels?.filter(
    (option: ConfiguredModelList) => option.model_purpose === "CHAT"
  );
  const embdOption = configuredModels?.filter(
    (option: ConfiguredModelList) => option.model_purpose === "EMBEDDING"
  );
  const rerankOption = configuredModels?.filter(
    (option: ConfiguredModelList) => option.model_purpose === "RERANK"
  );
  const nl2sqlOption = configuredModels?.filter(
    (option: ConfiguredModelList) => option.model_purpose === "NL2SQL"
  );
  const nl2codeOption = configuredModels?.filter(
    (option: ConfiguredModelList) => option.model_purpose === "NL2PYTHON"
  );

  return {
    chatOption,
    embdOption,
    rerankOption,
    nl2sqlOption,
    nl2codeOption,
  };
};

/**
 * 获取没有配置的模型列表
 */
export const useUnconfiguredModels = () => {
  return useQuery({
    queryKey: ["unconfiguredModels"],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<Model[]>(`/describe-model-factories`);
    },
  });
};

/**
 * 删除某个模型
 */
export const useDeleteModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { llm_id: string }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/delete-model`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["existingModels"] });
    },
  });
};

/**
 * 配置模型
 */
export const useConfigureModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      llm_factory_id: string;
      llm_name: string;
      model_type: string;
      api_key?: string;
      base_url?: string;
    }) => {
      const apiClient = await getApiClient();

      return apiClient.post(`/configure-model`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["existingModels"] });
      queryClient.invalidateQueries({ queryKey: ["unconfiguredModels"] });
    },
  });
};

export const useConfigurabledModels = (id: string) => {
  return useQuery({
    queryKey: ["configurabledModels", id],
    queryFn: async () => {
      const apiClient = await getApiClient();

      return apiClient.post<ConfigurabledModel[]>(`/describe-configurable-models`, {
        llm_factory_id: id,
      });
    },
    enabled: !!id,
  });
};
