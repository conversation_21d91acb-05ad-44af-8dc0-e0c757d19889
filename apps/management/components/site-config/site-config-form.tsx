"use client";

import {
  useInitSite,
  useSiteUploadFile,
  useUpdateSiteConfig,
  useUploadFile,
} from "@/service/site-config-service";
import { siteConfigAtom } from "@/store/user-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ragtop-web/ui/components/button";
import { DialogFooter } from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useAtomValue } from "jotai";
import { Plus, X } from "lucide-react";
import Image from "next/image";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式
const createFormSchema = (isInit: boolean) =>
  z.object({
    login_name: isInit ? z.string().min(1, "管理端登录用户名不能为空") : z.string().optional(),
    password: isInit ? z.string().min(1, "管理端登录密码不能为空") : z.string().optional(),
    logo: z.string().optional(),
    dark_logo: z.string().optional(),
    mini_logo: z.string().optional(),
    mini_dark_logo: z.string().optional(),
    favicon: z.string().optional(),
    ...(isInit
      ? {
          site_name: z.string().min(1, "站点名称不能为空"),
        }
      : {
          name: z.string().min(1, "站点名称不能为空"),
        }),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

export function SiteConfigForm({
  isInit = false,
  onClose,
}: {
  isInit?: boolean;
  onClose?: () => void;
}) {
  const { toast } = useToast();
  const config = useAtomValue(siteConfigAtom);
  const updateSiteConfig = useUpdateSiteConfig();
  const initSiteConfig = useInitSite();
  const uploadFile = useUploadFile();
  const siteUploadFile = useSiteUploadFile();
  const uploadFunc = isInit ? siteUploadFile : uploadFile;
  const siteConfigFunc = isInit ? initSiteConfig : updateSiteConfig;

  // 创建对应的表单验证模式
  const formSchema = createFormSchema(isInit);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      login_name: isInit ? "" : undefined,
      password: isInit ? "" : undefined,
      site_name: isInit ? "" : undefined,
      name: isInit ? undefined : "",
      logo: "",
      dark_logo: "",
      mini_dark_logo: "",
      mini_logo: "",
      favicon: "",
    },
  });

  // 当config变化时更新表单值
  useEffect(() => {
    if (config && !isInit) {
      form.reset({
        name: config.name || "",
        logo: config.logo || "",
        dark_logo: config.dark_logo || "",
        mini_dark_logo: config.mini_dark_logo || "",
        mini_logo: config.mini_logo || "",
        favicon: config.favicon || "",
      });
    }
  }, [config, form, isInit]);

  const handleClose = () => {
    form.reset();
    onClose?.();
  };

  const handleFileUpload = async (
    file: File,
    type: "logo" | "mini_logo" | "favicon" | "dark_logo" | "mini_dark_logo"
  ) => {
    try {
      uploadFunc.mutate(file, {
        onSuccess: (response) => {
          // Assuming the response contains the file URL or path
          const fileUrl = response;

          form.setValue(type, fileUrl, {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
          });
          toast({
            title: "成功",
            description: "文件上传成功",
          });
        },
        onError: (error) => {
          console.error("文件上传失败:", error);
          toast({
            title: "错误",
            description: "文件上传失败",
            variant: "destructive",
          });
        },
      });
    } catch (error) {
      console.error("Failed to upload file:", error);
      toast({
        title: "错误",
        description: "文件上传失败",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (values: FormValues) => {
    siteConfigFunc.mutate(values, {
      onSuccess: () => {
        toast({
          title: "成功",
          description: "站点配置更新成功",
        });

        handleClose();
      },
      onError: (error) => {
        console.error("Failed to update site config:", error);
        toast({
          title: "错误",
          description: "站点配置更新失败",
          variant: "destructive",
        });
      },
    });
  };

  // Add new function to handle image deletion
  const handleDeleteImage = (
    type: "logo" | "mini_logo" | "favicon" | "dark_logo" | "mini_dark_logo"
  ) => {
    form.setValue(type, "", {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="flex min-h-0 flex-1 flex-col">
        <div className="no-scrollbar flex-1 overflow-y-auto">
          <div className="space-y-6 pb-6">
            {isInit && (
              <>
                <FormField
                  control={form.control}
                  name="login_name"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredFormLabel>管理端登录用户名</RequiredFormLabel>
                      <FormControl>
                        <Input placeholder="请输入管理端登录用户名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredFormLabel>管理端登录密码</RequiredFormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请初始化管理端登录密码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name={isInit ? "site_name" : "name"}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>站点名称</RequiredFormLabel>
                  <FormControl>
                    <Input placeholder="请输入站点名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo</FormLabel>
                    <FormDescription>建议尺寸：140px * 40px, 该图标显示在登录页中</FormDescription>
                    <div className="flex items-center gap-4">
                      {field.value ? (
                        <div className="group relative h-32 w-32">
                          <Image
                            src={`/frontend-api/image-proxy?key=${field.value}`}
                            alt="Logo"
                            fill
                            className="object-contain"
                            unoptimized
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-2 right-2 size-6 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-black/70"
                            onClick={() => handleDeleteImage("logo")}
                          >
                            <X className="size-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="relative">
                          <Input
                            type="file"
                            accept="image/*"
                            className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              const file = e.target.files?.[0];

                              if (file) {handleFileUpload(file, "logo");}
                            }}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            className="flex h-32 w-32 flex-col items-center justify-center gap-2 border-dashed"
                          >
                            <Plus className="size-6" />
                            <span className="text-xs">上传图片</span>
                          </Button>
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="dark_logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dark Logo</FormLabel>
                    <FormDescription>
                      建议尺寸：140px * 40px, 该图标显示在暗黑模式下的登录页中
                    </FormDescription>
                    <div className="flex items-center gap-4">
                      {field.value ? (
                        <div className="group relative h-32 w-32">
                          <Image
                            src={`/frontend-api/image-proxy?key=${field.value}`}
                            alt="Logo"
                            fill
                            className="object-contain"
                            unoptimized
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-2 right-2 size-6 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-black/70"
                            onClick={() => handleDeleteImage("dark_logo")}
                          >
                            <X className="size-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="relative">
                          <Input
                            type="file"
                            accept="image/*"
                            className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              const file = e.target.files?.[0];

                              if (file) {handleFileUpload(file, "dark_logo");}
                            }}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            className="flex h-32 w-32 flex-col items-center justify-center gap-2 border-dashed"
                          >
                            <Plus className="size-6" />
                            <span className="text-xs">上传图片</span>
                          </Button>
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mini_logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mini Logo</FormLabel>
                    <FormDescription>建议尺寸：40px * 40px, 该图标显示在导航栏中</FormDescription>
                    <div className="flex items-center gap-4">
                      {field.value ? (
                        <div className="group relative h-16 w-16">
                          <Image
                            src={`/frontend-api/image-proxy?key=${field.value}`}
                            alt="Mini Logo"
                            fill
                            className="object-contain"
                            unoptimized
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-1 right-1 size-5 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-black/70"
                            onClick={() => handleDeleteImage("mini_logo")}
                          >
                            <X className="size-2.5" />
                          </Button>
                        </div>
                      ) : (
                        <div className="relative">
                          <Input
                            type="file"
                            accept="image/*"
                            className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              const file = e.target.files?.[0];

                              if (file) {handleFileUpload(file, "mini_logo");}
                            }}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            className="flex h-16 w-16 flex-col items-center justify-center gap-1 border-dashed"
                          >
                            <Plus className="size-4" />
                            <span className="text-[10px]">上传</span>
                          </Button>
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="mini_dark_logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mini Dark Logo</FormLabel>
                    <FormDescription>
                      建议尺寸：40px * 40px, 该图标显示在暗黑模式下的导航栏中
                    </FormDescription>
                    <div className="flex items-center gap-4">
                      {field.value ? (
                        <div className="group relative h-16 w-16">
                          <Image
                            src={`/frontend-api/image-proxy?key=${field.value}`}
                            alt="Mini Logo"
                            fill
                            className="object-contain"
                            unoptimized
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-1 right-1 size-5 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-black/70"
                            onClick={() => handleDeleteImage("mini_dark_logo")}
                          >
                            <X className="size-2.5" />
                          </Button>
                        </div>
                      ) : (
                        <div className="relative">
                          <Input
                            type="file"
                            accept="image/*"
                            className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              const file = e.target.files?.[0];

                              if (file) {handleFileUpload(file, "mini_dark_logo");}
                            }}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            className="flex h-16 w-16 flex-col items-center justify-center gap-1 border-dashed"
                          >
                            <Plus className="size-4" />
                            <span className="text-[10px]">上传</span>
                          </Button>
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="favicon"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Favicon</FormLabel>
                    <FormDescription>
                      建议尺寸：16px * 16px, 该图标显示在浏览器标签中
                    </FormDescription>
                    <div className="flex items-center gap-4">
                      {field.value ? (
                        <div className="group relative h-8 w-8">
                          <img
                            src={`/frontend-api/image-proxy?key=${field.value}`}
                            alt="Favicon"
                            width={32}
                            height={32}
                            className="object-contain"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute -top-1 -right-1 size-4 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-black/70"
                            onClick={() => handleDeleteImage("favicon")}
                          >
                            <X className="size-2" />
                          </Button>
                        </div>
                      ) : (
                        <div className="relative">
                          <Input
                            type="file"
                            accept="image/*"
                            className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              const file = e.target.files?.[0];

                              if (file) {handleFileUpload(file, "favicon");}
                            }}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            className="flex h-8 w-8 items-center justify-center border-dashed"
                          >
                            <Plus className="size-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        <DialogFooter className="border-t">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={siteConfigFunc.isPending || uploadFile.isPending}
          >
            取消
          </Button>
          <Button type="submit" disabled={siteConfigFunc.isPending}>
            {siteConfigFunc.isPending ? (
              <span className="flex items-center gap-2">
                <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                提交中...
              </span>
            ) : (
              "保存"
            )}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
