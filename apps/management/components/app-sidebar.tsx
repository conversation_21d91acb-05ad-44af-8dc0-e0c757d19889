"use client";

import { Component, UserCog, Users } from "lucide-react";
import * as React from "react";

import { NavProjects } from "@/components/nav/nav-projects";
import { NavUser } from "@/components/nav/nav-user";
import { siteConfigAtom } from "@/store/user-store";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarRail,
} from "@ragtop-web/ui/components/sidebar";
import { useAtomValue } from "jotai";
import { useTheme } from "next-themes";
import Image from "next/image";

const data = {
  projects: [
    {
      name: "账户管理",
      url: "/users",
      icon: UserCog,
    },
    {
      name: "团队管理",
      url: "/teams",
      icon: Users,
    },
    {
      name: "模型配置",
      url: "/models",
      icon: Component,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const theme = useTheme();
  const siteConfig = useAtomValue(siteConfigAtom);
  const isDark = theme.resolvedTheme === "dark";

  return (
    <Sidebar collapsible="icon" variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem className="flex flex-row items-center gap-2 p-0">
            {siteConfig && (
              <Image
                src={
                  isDark
                    ? `/frontend-api/image-proxy?key=${siteConfig?.mini_dark_logo}`
                    : `/frontend-api/image-proxy?key=${siteConfig?.mini_logo}`
                }
                alt="logo"
                width={40}
                height={40}
                className="shrink-0"
                unoptimized
              />
            )}
            <div className="text-2xl font-medium group-data-[collapsible=icon]:hidden">
              {siteConfig?.name}
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
