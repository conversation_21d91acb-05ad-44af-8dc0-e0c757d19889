// app/api/image-proxy/route.ts
import { connection, NextRequest } from "next/server";

function getRealOrigin(request: NextRequest): string {
  const proto = request.headers.get("x-forwarded-proto") || "http";
  const host = request.headers.get("x-forwarded-host") || request.headers.get("host");

  return `${proto}://${host}`;
}

function getBaseUrl(req: NextRequest, apiBaseUrl: string | undefined) {
  if (apiBaseUrl) {
    return apiBaseUrl;
  }

  return getRealOrigin(req);
}

export async function GET(req: NextRequest) {
  await connection();
  const apiBaseUrl = process.env.MANAGEMENT_INNER_API_URL || "";
  const key = req.nextUrl.searchParams.get("key");

  if (!key) {
    return new Response("Missing key param", { status: 400 });
  }

  const baseUrl = getBaseUrl(req, apiBaseUrl);
  const targetUrl = `${baseUrl}/_${key}`;

  try {
    const fetchRes = await fetch(targetUrl);

    if (!fetchRes.ok) {
      return new Response(fetchRes.body, { status: fetchRes.status });
    }

    const contentType = fetchRes.headers.get("content-type") || "image/svg+xml";
    const buffer = await fetchRes.arrayBuffer();

    return new Response(Buffer.from(buffer), {
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=3600",
      },
    });
  } catch (err) {
    console.error("Image proxy error:", err, targetUrl);

    return new Response("Internal error", { status: 500 });
  }
}
