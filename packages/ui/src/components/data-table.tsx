"use client";

import {
  AccessorKeyColumnDef,
  ColumnDef,
  ColumnFiltersState,
  ColumnOrderState,
  ColumnPinningState,
  ColumnSizingState,
  createColumnHelper,
  ExpandedState,
  FilterFn,
  filterFns,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getGroupedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  GroupingState,
  PaginationState,
  RowSelectionState,
  SortingFn,
  sortingFns,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";

import { cn } from "#lib/utils.ts";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ragtop-web/ui/components/table";
import type React from "react";
import { useEffect, useRef, useState } from "react";

// 扩展列的meta配置
interface StickyColumnMeta {
  sticky?: "left" | "right" | false;
  stickyIndex?: number; // 在固定列中的索引，用于计算left/right值
}

// 计算固定列的位置和样式
function calculateStickyColumns<TData, TValue>(
  columns: ColumnDef<TData, TValue>[],
  showSeparators: boolean = true
) {
  let leftOffset = 0;
  let rightOffset = 0;
  const rightColumns: Array<{ index: number; width: number }> = [];

  // 先找出所有右侧固定列并计算它们的宽度
  columns.forEach((column, index) => {
    const meta = column.meta as any;

    if (meta?.sticky === "right") {
      const width = column.size || 120; // 默认宽度120px

      rightColumns.unshift({ index, width }); // 逆序添加，最右侧的列最先处理
    }
  });

  return columns.map((column, index) => {
    const meta = column.meta as any;

    if (!meta?.sticky) {return column;}

    let stickyStyle = "";
    let stickyPosition: React.CSSProperties = {};
    let cellStickyStyle = "";
    let headerStickyStyle = "";

    if (meta.sticky === "left") {
      stickyStyle = "sticky z-10";
      cellStickyStyle =
        "sticky z-10 bg-background group-hover:bg-muted group-data-[state=selected]:bg-muted";
      headerStickyStyle = "sticky z-10 bg-gray-100 dark:bg-gray-900";

      // 确保列宽度准确设置
      const width = column.size || (column.id === "select" ? 50 : 200);

      stickyPosition = {
        left: `${leftOffset}px`,
        width: `${width}px`,
        minWidth: `${width}px`,
        maxWidth: `${width}px`,
        boxSizing: "border-box",
      };

      // 检查是否是最后一个左侧固定列
      let lastLeftStickyIndex = -1;

      for (let i = columns.length - 1; i >= 0; i--) {
        const column = columns[i];

        if (column && (column.meta as any)?.sticky === "left") {
          lastLeftStickyIndex = i;
          break;
        }
      }
      const isLastLeftSticky = index === lastLeftStickyIndex;
      const borderStyle =
        isLastLeftSticky && showSeparators
          ? " border-r shadow-[1px_0_3px_-1px_rgba(0,0,0,0.08)]"
          : "";

      cellStickyStyle += borderStyle;
      headerStickyStyle += borderStyle;

      leftOffset += width;
    } else if (meta.sticky === "right") {
      const rightColumnData = rightColumns.find((col) => col.index === index);

      if (rightColumnData) {
        stickyStyle = "sticky z-10";
        cellStickyStyle =
          "sticky z-10 bg-background group-hover:bg-muted group-data-[state=selected]:bg-muted";
        headerStickyStyle = "sticky z-10 bg-gray-100 dark:bg-gray-900";
        stickyPosition = { right: `${rightOffset}px` };
        let firstRightStickyIndex = -1;

        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];

          if (column && (column.meta as any)?.sticky === "right") {
            firstRightStickyIndex = i;
            break;
          }
        }
        const isLastRightSticky = index === firstRightStickyIndex;
        const borderStyle =
          isLastRightSticky && showSeparators
            ? " border-l shadow-[-1px_0_3px_-1px_rgba(0,0,0,0.08)]"
            : "";

        cellStickyStyle += borderStyle;
        headerStickyStyle += borderStyle;
        rightOffset += rightColumnData.width;
      }
    }

    return {
      ...column,
      meta: {
        ...meta,
        className: headerStickyStyle || stickyStyle,
        cellClassName: cellStickyStyle || stickyStyle,
        stickyStyle: stickyPosition,
      },
    };
  });
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  /** 是否启用选择功能 */
  enableSelection?: boolean;
  /** 选择状态变化回调 */
  onSelectionChange?: (selectedRows: TData[], selectedRowIds: string[]) => void;
  /** 外部控制的选择状态 */
  selectedRowIds?: string[];
  /** 获取行ID的函数 */
  getRowId?: (row: TData, index: number) => string;
  getRowCanSelect?: (row: TData) => boolean;
  /** 表格容器的类名 */
  containerClassName?: string;
  /** 表格本身的类名 */
  className?: string;
}

export {
  createColumnHelper,
  filterFns,
  // Core
  flexRender,
  getCoreRowModel,
  // Expanding
  getExpandedRowModel,
  getFacetedMinMaxValues,
  // Faceting
  getFacetedRowModel,
  getFacetedUniqueValues,
  // Filtering
  getFilteredRowModel,
  // Grouping
  getGroupedRowModel,
  // Pagination
  getPaginationRowModel,
  // Sorting
  getSortedRowModel,
  sortingFns,
  useReactTable,
};

export type {
  AccessorKeyColumnDef,
  // Core types
  ColumnDef,
  // State types
  ColumnFiltersState,
  ColumnOrderState,
  ColumnPinningState,
  ColumnSizingState,
  ExpandedState,
  // Function types
  FilterFn,
  GroupingState,
  PaginationState,
  RowSelectionState,
  SortingFn,
  SortingState,
  // Custom types
  StickyColumnMeta,
  VisibilityState,
};

/**
 * 创建固定列的helper函数
 */
export function createStickyColumn<TData, TValue>(
  column: ColumnDef<TData, TValue>,
  sticky: "left" | "right",
  width?: number
): ColumnDef<TData, TValue> {
  return {
    ...column,
    size: width || column.size,
    meta: {
      ...column.meta,
      sticky,
    },
  };
}

/**
 * 创建选择列的配置
 * @returns 选择列定义
 */
export const createSelectionColumn = <TData, TValue>(): ColumnDef<TData, TValue> => ({
  id: "select",
  header: ({ table }) => (
    <input
      type="checkbox"
      checked={table.getIsAllPageRowsSelected()}
      ref={(el) => {
        if (el) {
          el.indeterminate = table.getIsSomePageRowsSelected() && !table.getIsAllPageRowsSelected();
        }
      }}
      onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
      className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
      aria-label="Select all"
    />
  ),
  cell: ({ row }) => (
    <input
      type="checkbox"
      checked={row.getIsSelected()}
      onChange={(e) => row.toggleSelected(e.target.checked)}
      className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
      aria-label="Select row"
    />
  ),
  enableSorting: false,
  enableHiding: false,
  size: 40,
});

export function DataTable<TData, TValue>({
  columns,
  data,
  className,
  enableSelection = false,
  onSelectionChange,
  selectedRowIds,
  getRowId,
  getRowCanSelect,
  containerClassName,
}: DataTableProps<TData, TValue>) {
  const [columnSizing, setColumnSizing] = useState<ColumnSizingState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [showSeparators, setShowSeparators] = useState(true);
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // 检测表格是否需要滚动
  useEffect(() => {
    const checkScrollNeed = () => {
      if (tableContainerRef.current) {
        const container = tableContainerRef.current;
        const table = container.querySelector("table");

        if (table) {
          const needsScroll = table.scrollWidth > container.clientWidth;

          setShowSeparators(needsScroll);
        }
      }
    };

    // 延迟检测，确保DOM渲染完成
    const timeoutId = setTimeout(checkScrollNeed, 100);

    // 监听窗口大小变化
    window.addEventListener("resize", checkScrollNeed);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("resize", checkScrollNeed);
    };
  }, [data, columns]);

  // 处理固定列配置
  const processedColumns = calculateStickyColumns(columns, showSeparators);

  // 同步外部选择状态
  useEffect(() => {
    if (selectedRowIds) {
      const newSelection: RowSelectionState = {};

      selectedRowIds.forEach((id) => {
        newSelection[id] = true;
      });
      setRowSelection(newSelection);
    } else if (selectedRowIds === undefined) {
      // 如果外部没有传入selectedRowIds，保持内部状态
    } else {
      // 如果传入了空数组，清空选择
      setRowSelection({});
    }
  }, [selectedRowIds]);

  const table = useReactTable({
    data,
    columns: processedColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    columnResizeMode: "onChange",
    enableColumnResizing: false,
    enableRowSelection: enableSelection,
    getRowId,
    onRowSelectionChange: (updater) => {
      const newRowSelection = typeof updater === "function" ? updater(rowSelection) : updater;

      setRowSelection(newRowSelection);

      // 立即触发回调
      if (onSelectionChange) {
        const selectedIds = Object.keys(newRowSelection).filter((key) => newRowSelection[key]);

        // 根据选择的ID找到对应的行数据
        const selectedRows = data.filter((item, index) => {
          const rowId = getRowId ? getRowId(item, index) : `${index}`;

          return selectedIds.includes(rowId);
        });

        onSelectionChange(selectedRows, selectedIds);
      }
    },
    state: {
      columnSizing,
      rowSelection,
    },
    initialState: {
      columnSizing,
    },
    onColumnSizingChange: setColumnSizing,
  });

  return (
    <div
      ref={tableContainerRef}
      className={cn("h-full rounded-md border", containerClassName || "overflow-auto")}
    >
      <Table
        style={{
          width: "100%",
          borderSpacing: 0,
          borderCollapse: "collapse",
        }}
        className={cn("border-collapse", className)}
      >
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const hasSize = header.column.columnDef.size !== undefined;
                const size = hasSize ? header.getSize() : undefined;
                const meta = header.column.columnDef.meta as any;
                const metaClassName = meta?.className;
                const stickyStyle = meta?.stickyStyle;
                const headerStyle = {
                  ...(hasSize && !stickyStyle?.width ? { width: `${size}px` } : {}),
                  ...stickyStyle,
                };

                return (
                  <TableHead
                    key={header.id}
                    style={headerStyle}
                    className={cn(
                      "border-b font-bold text-gray-700 dark:text-gray-200",
                      stickyStyle?.width ? "overflow-hidden !px-2 !pr-2" : "",
                      metaClassName || "bg-gray-100 dark:bg-gray-900"
                    )}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                    {/* 拖拽手柄 */}
                    {header.column.getCanResize() ? (
                      <div
                        {...{
                          onMouseDown: header.getResizeHandler(),
                          onTouchStart: header.getResizeHandler(),
                          className:
                            "inline-block w-1 h-full absolute right-0 top-0 cursor-col-resize",
                        }}
                      />
                    ) : null}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
                className="group"
              >
                {row.getVisibleCells().map((cell) => {
                  const meta = cell.column.columnDef.meta as any;
                  const cellClassName = meta?.cellClassName || meta?.className;
                  const stickyStyle = meta?.stickyStyle;
                  const cellStyle = {
                    ...(stickyStyle?.width ? {} : { width: cell.column.getSize() }),
                    ...stickyStyle,
                  };

                  return (
                    <TableCell
                      key={cell.id}
                      style={cellStyle}
                      className={cn(
                        stickyStyle?.width ? "overflow-hidden !p-2" : "",
                        cellClassName
                      )}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={processedColumns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
