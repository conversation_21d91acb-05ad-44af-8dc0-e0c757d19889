"use client";

import React, { useEffect, useRef } from "react";
import { cn } from "@ragtop-web/ui/lib/utils";
import { Loader2 } from "lucide-react";

/**
 * 无限滚动列表配置接口
 */
export interface InfiniteScrollListProps<T> {
  /** 数据列表 */
  data: T[];
  /** 是否加载中 */
  isLoading?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 是否初始加载中 */
  isInitialLoading?: boolean;
  /** 是否正在获取下一页 */
  isFetchingNextPage?: boolean;
  /** 错误信息 */
  error?: string | null;
  /** 加载下一页函数 */
  fetchNextPage?: () => void;
  /** 重新加载函数 */
  reload?: () => void;
  /** 渲染单个项目的函数 */
  renderItem: (item: T, index: number) => React.ReactNode;
  /** 渲染空状态的函数 */
  renderEmpty?: () => React.ReactNode;
  /** 渲染错误状态的函数 */
  renderError?: (error: string, reload?: () => void) => React.ReactNode;
  /** 渲染加载状态的函数 */
  renderLoading?: () => React.ReactNode;
  renderLoadDone?: () => React.ReactNode;
  /** 容器类名 */
  className?: string;
  /** 列表容器类名 */
  listClassName?: string;
  /** 项目容器类名 */
  itemClassName?: string;
  /** 距离底部多少像素时触发加载，默认 200px */
  threshold?: number;
  /** 是否启用自动滚动检测，默认 true */
  enableScrollDetection?: boolean;
}

/**
 * 无限滚动列表组件
 *
 * 提供通用的无限滚动列表功能，支持自定义渲染和样式
 */
export function InfiniteScrollList<T>({
  data,
  isLoading = false,
  hasMore = false,
  isInitialLoading = false,
  isFetchingNextPage = false,
  error = null,
  fetchNextPage,
  reload,
  renderItem,
  renderEmpty,
  renderError,
  renderLoading,
  renderLoadDone,
  className,
  listClassName,
  itemClassName,
  threshold = 50,
  enableScrollDetection = true,
}: InfiniteScrollListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // 使用 Intersection Observer 检测滚动到底部
  useEffect(() => {
    if (!enableScrollDetection || !fetchNextPage || !hasMore || isLoading || isFetchingNextPage) {
      return;
    }

    const loadMoreElement = loadMoreRef.current;

    if (!loadMoreElement) {return;}

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;

        if (entry?.isIntersecting && hasMore && !isLoading && !isFetchingNextPage) {
          fetchNextPage();
        }
      },
      {
        rootMargin: `${threshold}px`,
      }
    );

    observerRef.current.observe(loadMoreElement);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enableScrollDetection, fetchNextPage, hasMore, isLoading, isFetchingNextPage, threshold]);

  // 默认渲染函数
  const defaultRenderEmpty = () => (
    <div className="text-muted-foreground flex items-center justify-center py-2">
      <div className="text-center">
        <p>暂无数据</p>
      </div>
    </div>
  );

  const defaultRenderLoadDone = () => (
    <div className="text-muted-foreground flex items-center justify-center py-2 text-sm">
      已加载全部数据
    </div>
  );

  const defaultRenderError = (errorMessage: string, reloadFn?: () => void) => (
    <div className="text-destructive flex items-center justify-center py-12">
      <div className="space-y-2 text-center">
        <p>加载失败: {errorMessage}</p>
        {reloadFn && (
          <button onClick={reloadFn} className="text-primary text-sm hover:underline">
            点击重试
          </button>
        )}
      </div>
    </div>
  );

  const defaultRenderLoading = () => (
    <div className="flex items-center justify-center py-12">
      <div className="text-muted-foreground flex items-center gap-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>加载中...</span>
      </div>
    </div>
  );

  // 如果初始加载中，显示加载状态
  if (isInitialLoading) {
    return (
      <div className={cn("w-full", className)} ref={containerRef}>
        {renderLoading ? renderLoading() : defaultRenderLoading()}
      </div>
    );
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className={cn("w-full", className)} ref={containerRef}>
        {renderError ? renderError(error, reload) : defaultRenderError(error, reload)}
      </div>
    );
  }

  // 如果没有数据，显示空状态
  if (!data || data.length === 0) {
    return (
      <div className={cn("w-full", className)} ref={containerRef}>
        {renderEmpty ? renderEmpty() : defaultRenderEmpty()}
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)} ref={containerRef}>
      {/* 数据列表 */}
      <div className={cn("overflow-y-auto", listClassName)}>
        {data.map((item, index) => (
          <div key={index} className={cn(itemClassName)}>
            {renderItem(item, index)}
          </div>
        ))}
      </div>

      {/* 加载更多指示器 */}
      {hasMore && (
        <div ref={loadMoreRef} className="flex items-center justify-center py-4">
          {isFetchingNextPage ? (
            <div className="text-muted-foreground flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>加载更多...</span>
            </div>
          ) : (
            <div className="text-muted-foreground text-sm">滚动加载更多</div>
          )}
        </div>
      )}

      {/* 已加载完毕提示 */}
      {!hasMore && data.length > 0 && (
        <>{renderLoadDone ? renderLoadDone() : defaultRenderLoadDone()}</>
      )}
    </div>
  );
}
