"use client";

import { cn } from "#lib/utils.ts";
import * as React from "react";
import { DataTable, type ColumnDef } from "./data-table.tsx";
import { Pagination } from "./pagination.tsx";

/**
 * 分页表格的数据响应接口
 */
export interface PaginatedResponse<T> {
  /** 数据记录 */
  records: T[];
  /** 总条目数 */
  total: number;
  /** 当前页码 */
  page_number?: number;
  /** 每页大小 */
  page_size?: number;
}

/**
 * 分页表格组件的属性
 */
export interface PaginatedTableProps<T> {
  /** 表格列定义 */
  columns: ColumnDef<T>[];
  /** 分页数据 */
  data: PaginatedResponse<T> | undefined;
  /** 是否加载中 */
  isLoading?: boolean;
  /** 当前页码 */
  currentPage: number;
  /** 每页大小 */
  pageSize: number;
  /** 页码变化回调 */
  onPageChange: (page: number) => void;
  /** 每页大小变化回调 */
  onPageSizeChange?: (pageSize: number) => void;
  /** 可选的每页大小选项 */
  pageSizeOptions?: number[];
  /** 是否显示总数信息 */
  showTotal?: boolean;
  /** 是否显示每页大小选择器 */
  showPageSizeSelector?: boolean;
  /** 加载状态的自定义内容 */
  loadingContent?: React.ReactNode;
  /** 空状态的自定义内容 */
  emptyContent?: React.ReactNode;
  /** 表格容器的额外类名 */
  className?: string;
  /** 分页组件的额外类名 */
  paginationClassName?: string;
  /** 表格滚动容器的类名 */
  tableContainerClassName?: string;
  /** 表格本身的类名 */
  tableClassName?: string;
  /** 是否启用选择功能 */
  enableSelection?: boolean;
  /** 选择状态变化回调 */
  onSelectionChange?: (selectedRows: T[], selectedRowIds: string[]) => void;
  /** 外部控制的选择状态 */
  selectedRowIds?: string[];
  /** 当前页选择状态变化回调 */
  onCurrentPageSelectionChange?: (selectedRows: T[], isAllSelected: boolean) => void;
  /** 获取行ID的函数 */
  getRowId?: (row: T, index: number) => string;
}

/**
 * 默认加载状态组件
 */
const DefaultLoadingContent = () => (
  <div className="flex h-64 items-center justify-center rounded-md border">
    <div className="flex flex-col items-center gap-2">
      <div className="border-primary h-6 w-6 animate-spin rounded-full border-2 border-t-transparent"></div>
      <p className="text-muted-foreground text-sm">加载中...</p>
    </div>
  </div>
);

/**
 * 默认空状态组件
 */
const DefaultEmptyContent = () => (
  <div className="flex h-64 items-center justify-center rounded-md border">
    <div className="flex flex-col items-center gap-2">
      <p className="text-muted-foreground text-sm">暂无数据</p>
    </div>
  </div>
);

/**
 * 分页表格组件
 *
 * 集成了DataTable和Pagination组件，提供完整的分页表格功能
 *
 * @param props 组件属性
 * @returns 分页表格组件
 */
export function PaginatedTable<T>({
  columns,
  data,
  isLoading = false,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showTotal = true,
  showPageSizeSelector = true,
  loadingContent,
  emptyContent,
  className,
  paginationClassName,
  tableContainerClassName,
  tableClassName,
  enableSelection = false,
  onSelectionChange,
  selectedRowIds,
  onCurrentPageSelectionChange,
  getRowId,
}: PaginatedTableProps<T>) {
  // 提取数据
  const records = data?.records || [];
  const total = data?.total || 0;
  const pageCount = Math.ceil(total / pageSize);

  // 处理选择状态变化
  const handleSelectionChange = (selectedRows: T[], selectedRowIds: string[]) => {
    // 触发选择变化回调
    if (onSelectionChange) {
      onSelectionChange(selectedRows, selectedRowIds);
    }

    // 检查当前页是否全选
    const isAllSelected = selectedRows.length === records.length && records.length > 0;

    if (onCurrentPageSelectionChange) {
      onCurrentPageSelectionChange(selectedRows, isAllSelected);
    }
  };

  // 渲染加载状态
  if (isLoading) {
    return loadingContent || <DefaultLoadingContent />;
  }

  // 渲染空状态
  if (!isLoading && records.length === 0) {
    return emptyContent || <DefaultEmptyContent />;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 数据表格 */}
      <DataTable<T, any>
        columns={columns}
        data={records}
        enableSelection={enableSelection}
        onSelectionChange={handleSelectionChange}
        selectedRowIds={selectedRowIds}
        getRowId={getRowId}
        className={tableClassName}
        containerClassName={tableContainerClassName}
      />

      {/* 分页组件 */}
      {(pageCount > 1 || showTotal || showPageSizeSelector) && (
        <div className={cn("flex justify-center", paginationClassName)}>
          <Pagination
            pageCount={pageCount}
            currentPage={currentPage}
            onPageChange={onPageChange}
            total={total}
            pageSize={pageSize}
            onPageSizeChange={onPageSizeChange}
            pageSizeOptions={pageSizeOptions}
            showTotal={showTotal}
            showPageSizeSelector={showPageSizeSelector}
          />
        </div>
      )}
    </div>
  );
}
