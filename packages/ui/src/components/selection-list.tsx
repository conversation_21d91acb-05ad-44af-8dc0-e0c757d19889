"use client";

import { cn } from "@ragtop-web/ui/lib/utils";
import { Loader2, Search } from "lucide-react";
import React, { useEffect, useRef } from "react";
import { Checkbox } from "./checkbox.tsx";
import { Input } from "./input.tsx";
import { Label } from "./label.tsx";
import { RadioGroup, RadioGroupItem } from "./radio-group.tsx";
import { ScrollArea } from "./scroll-area.tsx";

/**
 * 选择项接口
 */
export interface SelectionItem {
  id: string;
  label: string;
  disabled?: boolean;
  [key: string]: any;
}

/**
 * 选择列表组件属性接口
 */
export interface SelectionListProps {
  /** 数据列表 */
  items: SelectionItem[];
  /** 是否加载中 */
  isLoading?: boolean;
  /** 是否正在加载下一页 */
  isLoadingMore?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 搜索关键词 */
  searchTerm?: string;
  /** 搜索关键词变化回调 */
  onSearchChange?: (term: string) => void;
  /** 滚动事件回调 */
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
  /** 选择模式：single 单选，multiple 多选 */
  selectionMode?: "single" | "multiple";
  /** 已选择的项目 ID */
  selectedIds?: string | string[];
  /** 选择变化回调 */
  onSelectionChange?: (selectedIds: string | string[]) => void;
  /** 搜索框占位符 */
  searchPlaceholder?: string;
  /** 空状态文本 */
  emptyText?: string;
  /** 加载文本 */
  loadingText?: string;
  /** 到底文本 */
  endText?: string;
  /** 容器高度 */
  height?: string;
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 自定义渲染项目 */
  renderItem?: (
    item: SelectionItem,
    isSelected: boolean,
    isDisabled: boolean,
    onSelect: (itemId: string) => void
  ) => React.ReactNode;
  /** 额外的 CSS 类名 */
  className?: string;
}

/**
 * 选择列表组件
 *
 * 支持单选和多选模式，内置搜索、分页加载功能
 */
export function SelectionList({
  items = [],
  isLoading = false,
  isLoadingMore = false,
  hasMore = false,
  searchTerm = "",
  onSearchChange,
  onScroll,
  selectionMode = "multiple",
  selectedIds = selectionMode === "single" ? "" : [],
  onSelectionChange,
  searchPlaceholder = "搜索...",
  emptyText = "未找到匹配项",
  loadingText = "加载中...",
  endText = "已到达列表底部",
  height = "300px",
  showSearch = true,
  renderItem,
  className,
}: SelectionListProps) {
  // 滚动容器引用
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 绑定滚动事件到 ScrollArea 的 viewport
  useEffect(() => {
    if (!onScroll || !scrollAreaRef.current) {return;}

    // 查找 ScrollArea 内部的 viewport 元素
    const viewport = scrollAreaRef.current.querySelector("[data-radix-scroll-area-viewport]");

    if (!viewport) {return;}

    const handleScrollEvent = (e: Event) => {
      // 使用类型断言将原生事件转换为 React 事件
      // 这样可以确保 scrollTop, scrollHeight, clientHeight 等属性可用
      onScroll(e as unknown as React.UIEvent<HTMLDivElement>);
    };

    viewport.addEventListener("scroll", handleScrollEvent, { passive: true });

    return () => {
      viewport.removeEventListener("scroll", handleScrollEvent);
    };
  }, [onScroll]);

  /**
   * 处理单个项目选择
   */
  const handleItemSelect = (itemId: string, isDisabled: boolean) => {
    if (isDisabled || !onSelectionChange) {return;}

    if (selectionMode === "single") {
      onSelectionChange(itemId);
    } else {
      const currentSelected = Array.isArray(selectedIds) ? selectedIds : [];

      if (currentSelected.includes(itemId)) {
        onSelectionChange(currentSelected.filter((id) => id !== itemId));
      } else {
        onSelectionChange([...currentSelected, itemId]);
      }
    }
  };

  /**
   * 检查项目是否被选中
   */
  const isItemSelected = (itemId: string): boolean => {
    if (selectionMode === "single") {
      return selectedIds === itemId;
    } else {
      return Array.isArray(selectedIds) && selectedIds.includes(itemId);
    }
  };

  /**
   * 渲染默认项目
   */
  const renderDefaultItem = (item: SelectionItem, isSelected: boolean, isDisabled: boolean) => {
    const itemContent = (
      <div className="flex flex-1 items-center space-x-2">
        <Label className="flex-1 cursor-pointer">
          <span className={cn(isDisabled && "text-muted-foreground")}>{item.label}</span>
        </Label>
      </div>
    );

    if (selectionMode === "single") {
      return (
        <div
          key={item.id}
          className={cn(
            "flex cursor-pointer items-center space-x-2 rounded-md p-2 transition-colors",
            isSelected && "bg-accent",
            !isDisabled && "hover:bg-accent",
            isDisabled && "cursor-not-allowed opacity-50"
          )}
          onClick={() => handleItemSelect(item.id, isDisabled)}
        >
          <RadioGroupItem value={item.id} disabled={isDisabled} className="pointer-events-none" />
          {itemContent}
        </div>
      );
    } else {
      return (
        <div
          key={item.id}
          className={cn(
            "flex cursor-pointer items-center space-x-2 rounded-md p-2 transition-colors",
            isSelected && "bg-accent",
            !isDisabled && "hover:bg-accent",
            isDisabled && "cursor-not-allowed opacity-50"
          )}
          onClick={() => handleItemSelect(item.id, isDisabled)}
        >
          <Checkbox checked={isSelected} disabled={isDisabled} className="pointer-events-none" />
          {itemContent}
        </div>
      );
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索框 */}
      {showSearch && (
        <div className="relative">
          <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
          <Input
            placeholder={searchPlaceholder}
            className="pl-8"
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => onSearchChange?.(e.target.value)}
          />
        </div>
      )}

      {/* 列表容器 */}
      <div className="rounded-md border">
        <ScrollArea ref={scrollAreaRef} style={{ height }} className="p-2">
          <div className="space-y-2">
            {/* 单选模式需要 RadioGroup 包装 */}
            {selectionMode === "single" ? (
              <RadioGroup
                value={typeof selectedIds === "string" ? selectedIds : ""}
                onValueChange={(value: string) => onSelectionChange?.(value)}
              >
                {isLoading && items.length === 0 ? (
                  <div className="py-6 text-center">
                    <Loader2 className="mx-auto mb-2 h-5 w-5 animate-spin" />
                    <p className="text-muted-foreground text-sm">{loadingText}</p>
                  </div>
                ) : items.length === 0 ? (
                  <div className="text-muted-foreground py-6 text-center text-sm">{emptyText}</div>
                ) : (
                  items.map((item) => {
                    const isSelected = isItemSelected(item.id);
                    const isDisabled = item.disabled || false;

                    return renderItem
                      ? renderItem(item, isSelected, isDisabled, (itemId) =>
                          handleItemSelect(itemId, isDisabled)
                        )
                      : renderDefaultItem(item, isSelected, isDisabled);
                  })
                )}
              </RadioGroup>
            ) : (
              <>
                {isLoading && items.length === 0 ? (
                  <div className="py-6 text-center">
                    <Loader2 className="mx-auto mb-2 h-5 w-5 animate-spin" />
                    <p className="text-muted-foreground text-sm">{loadingText}</p>
                  </div>
                ) : items.length === 0 ? (
                  <div className="text-muted-foreground py-6 text-center text-sm">{emptyText}</div>
                ) : (
                  items.map((item) => {
                    const isSelected = isItemSelected(item.id);
                    const isDisabled = item.disabled || false;

                    return renderItem
                      ? renderItem(item, isSelected, isDisabled, (itemId) =>
                          handleItemSelect(itemId, isDisabled)
                        )
                      : renderDefaultItem(item, isSelected, isDisabled);
                  })
                )}
              </>
            )}

            {/* 加载更多指示器 */}
            {isLoadingMore && items.length > 0 && (
              <div className="py-2 text-center">
                <Loader2 className="mx-auto h-4 w-4 animate-spin" />
              </div>
            )}

            {/* 到底提示 */}
            {!isLoading && !hasMore && items.length > 0 && (
              <div className="text-muted-foreground py-2 text-center text-sm">{endText}</div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
