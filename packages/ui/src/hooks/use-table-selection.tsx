import { useCallback, useState } from "react";

/**
 * 表格选择状态管理 Hook
 */
export function useTableSelection<T = any>() {
  const [selectedRows, setSelectedRows] = useState<T[]>([]);
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [isCurrentPageAllSelected, setIsCurrentPageAllSelected] = useState(false);

  // 处理选择变化
  const handleSelectionChange = useCallback((rows: T[], ids: string[]) => {
    setSelectedRows(rows);
    setSelectedRowIds(ids);
  }, []);

  // 处理当前页选择状态变化
  const handleCurrentPageSelectionChange = useCallback((rows: T[], isAllSelected: boolean) => {
    setIsCurrentPageAllSelected(isAllSelected);
  }, []);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectedRows([]);
    setSelectedRowIds([]);
    setIsCurrentPageAllSelected(false);
  }, []);

  // 检查是否有选择项
  const hasSelection = selectedRows.length > 0;

  // 选择所有当前页项目
  const selectCurrentPage = useCallback((pageData: T[]) => {
    const ids = pageData.map((_, index) => `${index}`);

    setSelectedRows(pageData);
    setSelectedRowIds(ids);
    setIsCurrentPageAllSelected(true);
  }, []);

  return {
    // 状态
    selectedRows,
    selectedRowIds,
    isCurrentPageAllSelected,
    hasSelection,

    // 操作函数
    handleSelectionChange,
    handleCurrentPageSelectionChange,
    clearSelection,
    selectCurrentPage,

    // 设置函数（用于外部控制）
    setSelectedRowIds,
  };
}

/**
 * 批量操作 Hook
 */
export function useBatchOperations<T = any>(selectedRows: T[]) {
  const [isLoading, setIsLoading] = useState(false);

  // 执行批量操作
  const executeBatch = useCallback(
    async (
      operation: (items: T[]) => Promise<void>,
      options?: {
        onSuccess?: (count: number) => void;
        onError?: (error: any) => void;
      }
    ) => {
      if (selectedRows.length === 0) {
        options?.onError?.(new Error("没有选择任何项目"));

        return;
      }

      setIsLoading(true);
      try {
        await operation(selectedRows);
        options?.onSuccess?.(selectedRows.length);
      } catch (error) {
        options?.onError?.(error);
      } finally {
        setIsLoading(false);
      }
    },
    [selectedRows]
  );

  return {
    isLoading,
    executeBatch,
    selectedCount: selectedRows.length,
    hasSelection: selectedRows.length > 0,
  };
}
