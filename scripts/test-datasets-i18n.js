#!/usr/bin/env node

/**
 * 测试datasets组件的国际化键是否存在
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取翻译文件
const zhMessages = JSON.parse(
  fs.readFileSync(path.join(__dirname, "../apps/web/messages/zh.json"), "utf8")
);
const enMessages = JSON.parse(
  fs.readFileSync(path.join(__dirname, "../apps/web/messages/en.json"), "utf8")
);

// 需要检查的翻译键
const requiredKeys = [
  "datasets.tabs.fileDataset",
  "datasets.tabs.databaseDataset",
  "datasets.fileForm.name",
  "datasets.fileForm.nameRequired",
  "datasets.fileForm.namePlaceholder",
  "datasets.fileForm.description",
  "datasets.fileForm.descriptionRequired",
  "datasets.fileForm.descriptionPlaceholder",
  "datasets.fileForm.supportInfo",
  "datasets.fileForm.exampleLabel",
  "datasets.fileForm.save",
  "datasets.card.deleteTitle",
  "datasets.card.deleteDescription",
];

function getNestedValue(obj, path) {
  return path.split(".").reduce((current, key) => current && current[key], obj);
}

function checkKeys(messages, lang) {
  console.log(`\n检查 ${lang} 翻译文件:`);
  let allExists = true;

  requiredKeys.forEach((key) => {
    const value = getNestedValue(messages, key);
    if (value) {
      console.log(`✅ ${key}: "${value}"`);
    } else {
      console.log(`❌ ${key}: 不存在`);
      allExists = false;
    }
  });

  return allExists;
}

console.log("🔍 检查datasets组件国际化键...");

const zhExists = checkKeys(zhMessages, "中文");
const enExists = checkKeys(enMessages, "英文");

if (zhExists && enExists) {
  console.log("\n🎉 所有翻译键都存在！");
  process.exit(0);
} else {
  console.log("\n❌ 存在缺失的翻译键");
  process.exit(1);
}
