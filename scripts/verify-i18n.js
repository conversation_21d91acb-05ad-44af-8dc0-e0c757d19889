#!/usr/bin/env node

/**
 * 验证国际化键是否存在
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取翻译文件
const zhMessages = JSON.parse(
  fs.readFileSync(path.join(__dirname, "../apps/web/messages/zh.json"), "utf8")
);

console.log("🔍 验证中文翻译文件...");
console.log("datasets存在:", !!zhMessages.datasets);
console.log("datasets.tabs存在:", !!zhMessages.datasets?.tabs);
console.log("datasets.fileForm存在:", !!zhMessages.datasets?.fileForm);
console.log("datasets.card存在:", !!zhMessages.datasets?.card);

if (zhMessages.datasets?.tabs) {
  console.log("✅ datasets.tabs.fileDataset:", zhMessages.datasets.tabs.fileDataset);
  console.log("✅ datasets.tabs.databaseDataset:", zhMessages.datasets.tabs.databaseDataset);
}

if (zhMessages.datasets?.fileForm) {
  console.log("✅ datasets.fileForm.name:", zhMessages.datasets.fileForm.name);
  console.log("✅ datasets.fileForm.save:", zhMessages.datasets.fileForm.save);
}

if (zhMessages.datasets?.card) {
  console.log("✅ datasets.card.deleteTitle:", zhMessages.datasets.card.deleteTitle);
  console.log("✅ datasets.card.deleteDescription:", zhMessages.datasets.card.deleteDescription);
}

// 调试：打印整个datasets对象的键
console.log("\n🔍 datasets对象的所有键:");
if (zhMessages.datasets) {
  Object.keys(zhMessages.datasets).forEach((key) => {
    console.log(`- ${key}:`, typeof zhMessages.datasets[key]);
  });

  // 检查是否现在有tabs和fileForm
  console.log("\n🔍 重新检查:");
  console.log("datasets.tabs存在:", !!zhMessages.datasets.tabs);
  console.log("datasets.fileForm存在:", !!zhMessages.datasets.fileForm);
}

// 调试：打印card对象的内容
console.log("\n🔍 datasets.card对象的内容:");
if (zhMessages.datasets?.card) {
  Object.keys(zhMessages.datasets.card).forEach((key) => {
    console.log(`- card.${key}:`, zhMessages.datasets.card[key]);
  });
}

console.log("\n🎉 验证完成！");
